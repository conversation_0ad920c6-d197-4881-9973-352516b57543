# View Tracking Setup Guide

## 📊 Current Status

Your **Stuthi** app already has a comprehensive view tracking system in place! Here's what exists:

### ✅ Database Schema (Already Complete)
- **Song**: `viewCount`, `uniqueViewers`, `lastViewed`
- **Artist**: `viewCount`, `uniqueViewers`, `lastViewed`  
- **Collection**: `viewCount`, `uniqueViewers`, `lastViewed`
- **ContentView**: Detailed view tracking with timestamps, sessions, sources
- **Analytics tables**: DailyMetrics, UserSession, PageView

### ✅ Backend API (Already Complete)
- **Analytics Service**: Full implementation with caching
- **Tracking Controller**: `/analytics/track/view` endpoint
- **Session Management**: User session tracking
- **Error Handling**: Graceful degradation

### ✅ Flutter Integration (Just Added)
- **AnalyticsService**: New Flutter service for tracking
- **Song Detail Screen**: Now tracks song views automatically
- **Error Handling**: Analytics won't break the app

## 🔧 Setup Required

### 1. Create Database Triggers

The only missing piece is the database triggers that automatically update view counts. Run this command:

```bash
cd chords-api
chmod +x scripts/setup-view-tracking.sh
./scripts/setup-view-tracking.sh
```

This will:
- Create PostgreSQL triggers to auto-update view counts
- Add performance indexes
- Set up unique viewer tracking

### 2. Test the System

After setting up triggers, test the view tracking:

```bash
cd chords-api
node scripts/test-view-tracking.js
```

This will:
- Test song, artist, and collection view tracking
- Verify view counts are incrementing
- Test unique viewer functionality
- Show before/after view counts

## 📱 How It Works

### When Users View Content:

1. **Song Detail Screen**: Automatically tracks when users view songs
2. **API Call**: Sends view data to `/analytics/track/view`
3. **Database Insert**: Creates `ContentView` record
4. **Trigger Fires**: Automatically updates `viewCount` and `uniqueViewers`
5. **Real-time Updates**: View counts update immediately

### View Tracking Features:

- **Total Views**: Counts every view (including repeat views)
- **Unique Viewers**: Counts each user only once per content
- **Session Tracking**: Links views to user sessions
- **Source Tracking**: Knows how users found the content
- **Performance**: Cached and optimized for scale

## 🎯 Next Steps

### 1. Add View Tracking to More Screens

You can easily add view tracking to other screens:

```dart
// In any screen
final AnalyticsService _analytics = AnalyticsService();

// Track artist view
_analytics.trackArtistView(artistId, source: 'artist_screen');

// Track collection view  
_analytics.trackCollectionView(collectionId, source: 'collection_screen');
```

### 2. Initialize Analytics Session

Add this to your app startup (main.dart or login):

```dart
final AnalyticsService _analytics = AnalyticsService();
await _analytics.initializeSession(
  deviceInfo: 'Flutter App',
  appVersion: '1.0.0', 
  platform: 'mobile',
);
```

### 3. View Analytics Data

The view counts are available in your existing API responses:
- Songs include `viewCount`, `uniqueViewers`, `lastViewed`
- Artists include `viewCount`, `uniqueViewers`, `lastViewed`
- Collections include `viewCount`, `uniqueViewers`, `lastViewed`

## 🚀 Benefits

- **User Engagement**: Track which content is most popular
- **Content Strategy**: See what users actually view
- **Performance**: Optimized with caching and triggers
- **Privacy**: Respects user privacy with session-based tracking
- **Reliability**: Won't break the app if analytics fail

## 📈 Analytics Dashboard

You can build analytics dashboards using the data:
- Most viewed songs/artists/collections
- User engagement patterns
- Content discovery sources
- Daily/weekly/monthly trends

The system is ready to scale and provides rich analytics data for your Christian music platform!
