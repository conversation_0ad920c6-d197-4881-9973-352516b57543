import type { Metada<PERSON> } from "next";
import { Gei<PERSON>, <PERSON><PERSON>st_Mono } from "next/font/google";
import { ThemeProvider } from "@/components/theme-provider";
import { AuthProvider } from "@/contexts/auth-context";
import { Toaster } from "sonner";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Christian Chords Admin",
  description: "Admin dashboard for Christian Chords app",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <ThemeProvider attribute="class" defaultTheme="dark">
          <AuthProvider>
            {children}
            <Toaster richColors closeButton position="top-right" theme="dark" />
          </AuthProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
