{"name": "chords-api", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "build:render": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start -- --port 3001", "dev": "nest start --watch -- --port 3001", "start:dev": "nest start --watch -- --port 3001", "start:minimal": "MINIMAL_LOGS=true nest start --watch -- --port 3001", "start:debug": "nest start --debug --watch -- --port 3001", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "create:admin": "./scripts/create-super-admin.sh", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:studio": "prisma studio"}, "dependencies": {"@nestjs/cli": "^9.5.0", "@nestjs/common": "^9.4.3", "@nestjs/config": "^3.1.1", "@nestjs/core": "^9.4.3", "@nestjs/platform-express": "^9.4.3", "@nestjs/swagger": "^7.1.16", "@prisma/client": "^5.10.0", "@supabase/supabase-js": "^2.49.4", "@types/bcrypt": "^5.0.2", "@types/ioredis": "^4.28.10", "@types/sanitize-html": "^2.15.0", "@types/uuid": "^9.0.7", "bcrypt": "^5.1.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "csv-parse": "^5.6.0", "csv-stringify": "^6.5.2", "firebase-admin": "^11.11.1", "ioredis": "^5.6.1", "multer": "^1.4.5-lts.2", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "sanitize-html": "^2.16.0", "uuid": "^9.0.1"}, "devDependencies": {"@nestjs/schematics": "^9.2.0", "@nestjs/testing": "^9.4.3", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/multer": "^1.4.12", "@types/node": "^20.3.1", "@types/supertest": "^2.0.12", "@typescript-eslint/eslint-plugin": "^5.59.11", "@typescript-eslint/parser": "^5.59.11", "eslint": "^8.42.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "jest": "^29.5.0", "prettier": "^2.8.8", "prisma": "^5.10.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.5.2", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}