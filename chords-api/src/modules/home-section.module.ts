import { <PERSON>du<PERSON> } from '@nestjs/common';
import { HomeSectionController } from '../controllers/home/<USER>';
import { BannerItemController } from '../controllers/home/<USER>';
import { HomeSectionService } from '../services/home-section.service';
import { BannerItemService } from '../services/banner-item.service';
import { PrismaService } from '../services/prisma.service';

@Module({
  controllers: [HomeSectionController, BannerItemController],
  providers: [HomeSectionService, BannerItemService, PrismaService],
  exports: [HomeSectionService, BannerItemService]
})
export class HomeSectionModule {}
