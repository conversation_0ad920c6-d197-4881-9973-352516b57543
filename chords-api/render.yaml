services:
  # A Node.js web service
  - type: web
    name: chords-api
    env: node
    runtime: node
    nodeVersion: 18.x
    region: singapore
    plan: free
    buildCommand: npm install --legacy-peer-deps && npm install --save ts-loader && npx prisma generate && npm run build
    startCommand: npx prisma migrate deploy && node dist/main.js
    healthCheckPath: /api/health
    envVars:
      - key: NODE_ENV
        value: production
      - key: DATABASE_URL
        sync: false
      - key: FIREBASE_PROJECT_ID
        sync: false
      - key: FIREBASE_PRIVATE_KEY
        sync: false
      - key: FIREBASE_CLIENT_EMAIL
        sync: false
      - key: JWT_SECRET
        sync: false
      - key: SUPABASE_URL
        sync: false
      - key: SUPABASE_KEY
        sync: false
      - key: REDIS_URL
        sync: false
    autoDeploy: true
