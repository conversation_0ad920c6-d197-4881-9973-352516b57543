interface AdSpaceProps {
  className?: string;
  size?: "small" | "medium" | "large";
}

export function AdSpace({ className = "", size = "medium" }: AdSpaceProps) {
  const sizeClasses = {
    small: "h-24",
    medium: "h-32",
    large: "h-48"
  };
  
  return (
    <div className={`bg-neutral-900 border border-neutral-800 rounded-lg flex items-center justify-center ${sizeClasses[size]} ${className}`}>
      <div className="text-center">
        <div className="text-xs text-neutral-500 mb-1">Advertisement</div>
        <div className="text-xs text-neutral-600">Google AdSense</div>
      </div>
    </div>
  );
}
