import { Music2 } from "lucide-react";

export function Footer() {
  return (
    <footer className="border-t border-neutral-800 py-8">
      <div className="max-w-7xl mx-auto px-6">
        <div className="flex flex-col md:flex-row justify-between items-center">
          <div className="flex items-center space-x-2 mb-4 md:mb-0">
            <Music2 className="w-5 h-5" />
            <span className="font-semibold">Stuthi</span>
          </div>
          <div className="flex items-center space-x-6 text-sm text-neutral-400">
            <a href="#" className="hover:text-white transition-colors">About</a>
            <a href="#" className="hover:text-white transition-colors">Support</a>
            <a href="#" className="hover:text-white transition-colors">Privacy</a>
            <a href="#" className="hover:text-white transition-colors">Terms</a>
          </div>
        </div>
        <div className="mt-6 pt-6 border-t border-neutral-800 text-center text-sm text-neutral-400">
          <p>&copy; 2024 Stuthi. All rights reserved.</p>
        </div>
      </div>
    </footer>
  );
}
