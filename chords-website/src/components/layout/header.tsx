"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Music2, Download } from "lucide-react";

export function Header() {
  return (
    <nav className="sticky top-0 z-50 bg-black/95 backdrop-blur-md border-b border-neutral-800">
      <div className="max-w-7xl mx-auto px-6">
        <div className="flex justify-between items-center h-16">
          <div className="flex items-center space-x-8">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-white rounded-lg flex items-center justify-center">
                <Music2 className="w-5 h-5 text-black" />
              </div>
              <span className="text-lg font-semibold">Stuthi</span>
            </div>
            <div className="hidden md:flex items-center space-x-6 text-sm">
              <a href="#" className="text-neutral-400 hover:text-white transition-colors">Browse</a>
              <a href="#" className="text-neutral-400 hover:text-white transition-colors">Artists</a>
              <a href="#" className="text-neutral-400 hover:text-white transition-colors">Collections</a>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <Button variant="ghost" size="sm" className="text-neutral-400 hover:text-white">
              Sign In
            </Button>
            <Button size="sm" className="bg-white text-black hover:bg-neutral-200">
              <Download className="w-4 h-4 mr-2" />
              Get App
            </Button>
          </div>
        </div>
      </div>
    </nav>
  );
}
