import { Card, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { AdSpace } from "@/components/ui/ad-space";

// Mock trending songs data
const trendingSongs = [
  { id: 1, title: "Amazing Grace", artist: "<PERSON>" },
  { id: 2, title: "How Great Thou Art", artist: "<PERSON>" },
  { id: 3, title: "10,000 Reasons", artist: "<PERSON>" },
];

export function Sidebar() {
  return (
    <div className="hidden lg:block">
      <div className="sticky top-24 space-y-6">
        {/* Trending Now */}
        <Card className="bg-neutral-900 border-neutral-800">
          <CardContent className="p-6">
            <h3 className="font-semibold mb-4">Trending Now</h3>
            <div className="space-y-3">
              {trendingSongs.map((song, index) => (
                <div key={song.id} className="flex items-center space-x-3 cursor-pointer hover:bg-neutral-800 p-2 rounded transition-colors">
                  <div className="w-8 h-8 bg-neutral-700 rounded flex items-center justify-center text-xs font-medium">
                    {index + 1}
                  </div>
                  <div className="min-w-0 flex-1">
                    <p className="text-sm font-medium truncate">{song.title}</p>
                    <p className="text-xs text-neutral-400 truncate">{song.artist}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Sidebar Ad */}
        <AdSpace size="large" />

        {/* Quick Links */}
        <Card className="bg-neutral-900 border-neutral-800">
          <CardContent className="p-6">
            <h3 className="font-semibold mb-4">Quick Links</h3>
            <div className="space-y-2">
              <a href="#" className="block text-sm text-neutral-400 hover:text-white transition-colors">Browse All Songs</a>
              <a href="#" className="block text-sm text-neutral-400 hover:text-white transition-colors">Popular Artists</a>
              <a href="#" className="block text-sm text-neutral-400 hover:text-white transition-colors">New Releases</a>
              <a href="#" className="block text-sm text-neutral-400 hover:text-white transition-colors">Chord Charts</a>
              <Separator className="my-3 bg-neutral-700" />
              <a href="#" className="block text-sm text-neutral-400 hover:text-white transition-colors">Help & Support</a>
              <a href="#" className="block text-sm text-neutral-400 hover:text-white transition-colors">Submit a Song</a>
            </div>
          </CardContent>
        </Card>

        {/* Bottom Sidebar Ad */}
        <AdSpace size="medium" />
      </div>
    </div>
  );
}
