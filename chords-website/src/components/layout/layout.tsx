import { Header } from "./header";
import { Footer } from "./footer";
import { Sidebar } from "./sidebar";

interface LayoutProps {
  children: React.ReactNode;
  showSidebar?: boolean;
}

export function Layout({ children, showSidebar = true }: LayoutProps) {
  return (
    <div className="min-h-screen bg-black text-white">
      <Header />
      
      <main className="max-w-7xl mx-auto px-6 py-8">
        {showSidebar ? (
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Main Content Area */}
            <div className="lg:col-span-3">
              {children}
            </div>
            
            {/* Sidebar */}
            <Sidebar />
          </div>
        ) : (
          <div className="max-w-4xl mx-auto">
            {children}
          </div>
        )}
      </main>
      
      <Footer />
    </div>
  );
}
