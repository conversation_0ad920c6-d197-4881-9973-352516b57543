import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Stuti - Christian Song Chords & Lyrics",
  description: "Discover thousands of Christian song chords and lyrics. Perfect for worship leaders, musicians, and churches. Download our mobile app for offline access.",
  keywords: "christian songs, worship chords, church music, guitar chords, piano chords, worship lyrics, christian music",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="dark">
      <body
        className={`${inter.variable} font-sans antialiased bg-black text-white`}
      >
        {children}
      </body>
    </html>
  );
}
