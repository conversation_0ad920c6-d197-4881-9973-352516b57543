import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { AdSpace } from "@/components/ui/ad-space";
import { Layout } from "@/components/layout";
import {
  Search,
  Download,
  Play,
  MoreHorizontal,
  ArrowRight,
  Music2,
  Clock,
  TrendingUp,
  Headphones
} from "lucide-react";

// Mock data
const recentSongs = [
  { id: 1, title: "<PERSON> Grace", artist: "<PERSON>", duration: "4:32", key: "G" },
  { id: 2, title: "How Great Thou Art", artist: "<PERSON>", duration: "3:45", key: "C" },
  { id: 3, title: "10,000 Reasons", artist: "<PERSON>", duration: "5:02", key: "D" },
  { id: 4, title: "Cornerstone", artist: "Hills<PERSON>", duration: "6:15", key: "A" },
  { id: 5, title: "Good Good Father", artist: "<PERSON>", duration: "4:18", key: "G" },
];

const topArtists = [
  { id: 1, name: "Hillsong United", songs: 156 },
  { id: 2, name: "<PERSON>", songs: 89 },
  { id: 3, name: "Bethel Music", songs: 134 },
  { id: 4, name: "Elevation Worship", songs: 78 },
  { id: 5, name: "Passion", songs: 92 },
  { id: 6, name: "Jesus Culture", songs: 67 },
];

const collections = [
  { id: 1, name: "Christmas Worship", songs: 45 },
  { id: 2, name: "Easter Celebration", songs: 32 },
  { id: 3, name: "Contemporary Hits", songs: 89 },
  { id: 4, name: "Classic Hymns", songs: 67 },
];

export default function Home() {
  return (
    <Layout>
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center space-x-2 mb-2">
          <Clock className="w-5 h-5 text-neutral-400" />
          <span className="text-sm text-neutral-400">
            {new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
          </span>
        </div>
        <h1 className="text-4xl font-bold mb-2">Good evening</h1>
        <p className="text-neutral-400">Discover worship songs and chords for your ministry</p>
      </div>

      {/* Search */}
      <div className="mb-8">
        <div className="relative max-w-lg">
          <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-neutral-400 w-5 h-5" />
          <Input
            placeholder="Search songs, artists, collections..."
            className="pl-12 h-12 bg-neutral-900 border-neutral-700 focus:border-white text-white placeholder:text-neutral-400 rounded-full"
          />
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-3 gap-4 mb-8">
        <Card className="bg-neutral-900 border-neutral-800">
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold mb-1">5,247</div>
            <div className="text-sm text-neutral-400">Songs</div>
          </CardContent>
        </Card>
        <Card className="bg-neutral-900 border-neutral-800">
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold mb-1">1,203</div>
            <div className="text-sm text-neutral-400">Artists</div>
          </CardContent>
        </Card>
        <Card className="bg-neutral-900 border-neutral-800">
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold mb-1">52</div>
            <div className="text-sm text-neutral-400">Collections</div>
          </CardContent>
        </Card>
      </div>

      {/* Recently Added */}
      <div className="mb-12">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-2">
            <TrendingUp className="w-5 h-5 text-neutral-400" />
            <h2 className="text-2xl font-bold">Recently Added</h2>
          </div>
          <Button variant="ghost" size="sm" className="text-neutral-400 hover:text-white">
            Show all
            <ArrowRight className="w-4 h-4 ml-1" />
          </Button>
        </div>

        <div className="space-y-1">
          {recentSongs.map((song, index) => (
            <div
              key={song.id}
              className="group flex items-center justify-between p-4 rounded-lg hover:bg-neutral-900 transition-all duration-200 cursor-pointer"
            >
              <div className="flex items-center space-x-4">
                <div className="relative">
                  <div className="w-10 h-10 bg-neutral-800 rounded-lg flex items-center justify-center group-hover:bg-white group-hover:text-black transition-all duration-200">
                    <Play className="w-4 h-4" />
                  </div>
                  <div className="absolute -top-1 -right-1 w-4 h-4 bg-neutral-700 rounded-full flex items-center justify-center text-xs font-medium">
                    {index + 1}
                  </div>
                </div>
                <div className="min-w-0">
                  <h3 className="font-medium truncate">{song.title}</h3>
                  <p className="text-sm text-neutral-400 truncate">{song.artist}</p>
                </div>
              </div>
              <div className="flex items-center space-x-4 text-sm text-neutral-400">
                <Badge variant="outline" className="border-neutral-700 text-neutral-300 text-xs">
                  {song.key}
                </Badge>
                <span className="hidden sm:block">{song.duration}</span>
                <Button variant="ghost" size="sm" className="opacity-0 group-hover:opacity-100 transition-opacity">
                  <MoreHorizontal className="w-4 h-4" />
                </Button>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* First Ad Placement */}
      <AdSpace className="mb-12" size="medium" />

      {/* Top Artists */}
      <div className="mb-12">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-2">
            <Headphones className="w-5 h-5 text-neutral-400" />
            <h2 className="text-2xl font-bold">Top Artists</h2>
          </div>
          <Button variant="ghost" size="sm" className="text-neutral-400 hover:text-white">
            Show all
            <ArrowRight className="w-4 h-4 ml-1" />
          </Button>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
          {topArtists.map((artist) => (
            <Card key={artist.id} className="bg-neutral-900 border-neutral-800 hover:bg-neutral-800 transition-all duration-200 cursor-pointer group">
              <CardContent className="p-6">
                <div className="w-20 h-20 bg-gradient-to-br from-neutral-700 to-neutral-600 rounded-full mx-auto mb-4 flex items-center justify-center group-hover:scale-105 transition-transform">
                  <Music2 className="w-10 h-10 text-neutral-300" />
                </div>
                <h3 className="font-medium text-center mb-1 truncate">{artist.name}</h3>
                <p className="text-sm text-neutral-400 text-center">{artist.songs} songs</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Collections */}
      <div className="mb-12">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold">Collections</h2>
          <Button variant="ghost" size="sm" className="text-neutral-400 hover:text-white">
            Show all
            <ArrowRight className="w-4 h-4 ml-1" />
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {collections.map((collection) => (
            <Card key={collection.id} className="bg-neutral-900 border-neutral-800 hover:bg-neutral-800 transition-all duration-200 cursor-pointer group">
              <CardContent className="p-6">
                <div className="w-20 h-20 bg-gradient-to-br from-neutral-700 to-neutral-600 rounded-xl mb-4 flex items-center justify-center group-hover:scale-105 transition-transform">
                  <Music2 className="w-10 h-10 text-neutral-300" />
                </div>
                <h3 className="font-medium mb-2">{collection.name}</h3>
                <p className="text-sm text-neutral-400">{collection.songs} songs</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Second Ad Placement */}
      <AdSpace className="mb-12" size="medium" />

      {/* App Download CTA */}
      <div className="bg-gradient-to-r from-neutral-900 to-neutral-800 rounded-xl p-8 text-center mb-12 border border-neutral-700">
        <div className="w-16 h-16 bg-white rounded-xl flex items-center justify-center mx-auto mb-6">
          <Download className="w-8 h-8 text-black" />
        </div>
        <h2 className="text-2xl font-bold mb-4">Get the Mobile App</h2>
        <p className="text-neutral-400 mb-6 max-w-md mx-auto">
          Access thousands of songs offline, create setlists, and never miss a chord change.
        </p>
        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          <Button className="bg-white text-black hover:bg-neutral-200">
            <Download className="w-4 h-4 mr-2" />
            Download for iOS
          </Button>
          <Button variant="outline" className="border-neutral-600 hover:bg-neutral-700">
            <Download className="w-4 h-4 mr-2" />
            Download for Android
          </Button>
        </div>
      </div>
    </Layout>
  );
}