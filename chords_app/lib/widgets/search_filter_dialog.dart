import 'package:flutter/material.dart';
import '../models/search_filters.dart';
import '../models/song.dart';
import '../models/artist.dart';
import '../models/collection.dart';

class SearchFilterDialog extends StatefulWidget {
  final int tabIndex; // 0 = Songs, 1 = Artists, 2 = Collections
  final SongSearchFilters? songFilters;
  final ArtistSearchFilters? artistFilters;
  final CollectionSearchFilters? collectionFilters;
  final Function(SongSearchFilters) onSongFiltersApplied;
  final Function(ArtistSearchFilters) onArtistFiltersApplied;
  final Function(CollectionSearchFilters) onCollectionFiltersApplied;

  // Add data lists to generate dynamic filters
  final List<Song>? availableSongs;
  final List<Artist>? availableArtists;
  final List<Collection>? availableCollections;

  const SearchFilterDialog({
    super.key,
    required this.tabIndex,
    this.songFilters,
    this.artistFilters,
    this.collectionFilters,
    required this.onSongFiltersApplied,
    required this.onArtistFiltersApplied,
    required this.onCollectionFiltersApplied,
    this.availableSongs,
    this.availableArtists,
    this.availableCollections,
  });

  @override
  State<SearchFilterDialog> createState() => _SearchFilterDialogState();
}

class _SearchFilterDialogState extends State<SearchFilterDialog> {
  late SongSearchFilters _songFilters;
  late ArtistSearchFilters _artistFilters;
  late CollectionSearchFilters _collectionFilters;

  @override
  void initState() {
    super.initState();
    _songFilters = widget.songFilters ?? SongSearchFilters();
    _artistFilters = widget.artistFilters ?? ArtistSearchFilters();
    _collectionFilters = widget.collectionFilters ?? CollectionSearchFilters();
  }

  // Generate dynamic filter options based on available data
  List<FilterOption> _getAvailableKeys() {
    if (widget.availableSongs == null || widget.availableSongs!.isEmpty) {
      return [];
    }

    final availableKeys = widget.availableSongs!
        .map((song) => song.key)
        .where((key) => key.isNotEmpty)
        .toSet()
        .toList();

    availableKeys.sort();

    return availableKeys
        .map((key) => FilterOption(id: key, displayName: key))
        .toList();
  }

  List<FilterOption> _getAvailableDifficulties() {
    if (widget.availableSongs == null || widget.availableSongs!.isEmpty) {
      return [];
    }

    final availableDifficulties = widget.availableSongs!
        .map((song) => song.difficulty)
        .where((difficulty) => difficulty != null && difficulty.isNotEmpty)
        .cast<String>()
        .toSet()
        .toList();

    // Sort by difficulty order
    final difficultyOrder = ['BEGINNER', 'EASY', 'MEDIUM', 'HARD', 'EXPERT'];
    availableDifficulties.sort((a, b) {
      final aIndex = difficultyOrder.indexOf(a.toUpperCase());
      final bIndex = difficultyOrder.indexOf(b.toUpperCase());
      if (aIndex == -1 && bIndex == -1) return a.compareTo(b);
      if (aIndex == -1) return 1;
      if (bIndex == -1) return -1;
      return aIndex.compareTo(bIndex);
    });

    return availableDifficulties
        .map((difficulty) => FilterOption(
              id: difficulty.toUpperCase(),
              displayName: _formatDifficultyName(difficulty)
            ))
        .toList();
  }

  List<FilterOption> _getAvailableCapos() {
    if (widget.availableSongs == null || widget.availableSongs!.isEmpty) {
      return [];
    }

    final availableCapos = widget.availableSongs!
        .map((song) => song.capo)
        .where((capo) => capo != null && capo > 0)
        .cast<int>()
        .toSet()
        .toList();

    availableCapos.sort();

    return availableCapos
        .map((capo) => FilterOption(
              id: capo.toString(),
              displayName: 'Capo $capo'
            ))
        .toList();
  }

  List<FilterOption> _getAvailableTimeSignatures() {
    if (widget.availableSongs == null || widget.availableSongs!.isEmpty) {
      return [];
    }

    final availableTimeSignatures = widget.availableSongs!
        .map((song) => song.timeSignature)
        .where((timeSignature) => timeSignature != null && timeSignature.isNotEmpty)
        .cast<String>()
        .toSet()
        .toList();

    availableTimeSignatures.sort();

    return availableTimeSignatures
        .map((timeSignature) => FilterOption(
              id: timeSignature,
              displayName: timeSignature
            ))
        .toList();
  }

  String _formatDifficultyName(String difficulty) {
    switch (difficulty.toUpperCase()) {
      case 'BEGINNER':
        return 'Beginner';
      case 'EASY':
        return 'Easy';
      case 'MEDIUM':
        return 'Medium';
      case 'HARD':
        return 'Hard';
      case 'EXPERT':
        return 'Expert';
      default:
        return difficulty;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: const Color(0xFF1E1E1E),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Filter',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close, color: Colors.white),
                  onPressed: () => Navigator.of(context).pop(),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Filter content based on tab
            Flexible(
              child: SingleChildScrollView(
                child: _buildFilterContent(),
              ),
            ),

            const SizedBox(height: 16),

            // Action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                // Reset button
                TextButton(
                  onPressed: _resetFilters,
                  child: const Text(
                    'Reset',
                    style: TextStyle(
                      color: Colors.grey,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                // Apply button
                ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFFC19FFF), // Light purple/lavender
                    foregroundColor: Colors.white,
                  ),
                  onPressed: _applyFilters,
                  child: const Text('Apply'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterContent() {
    switch (widget.tabIndex) {
      case 0:
        return _buildSongFilters();
      case 1:
        return _buildArtistFilters();
      case 2:
        return _buildCollectionFilters();
      default:
        return const SizedBox.shrink();
    }
  }

  Widget _buildSongFilters() {
    // Get dynamic filter options
    final availableKeys = _getAvailableKeys();
    final availableDifficulties = _getAvailableDifficulties();
    final availableCapos = _getAvailableCapos();
    final availableTimeSignatures = _getAvailableTimeSignatures();

    // Check if we have any filter options available
    final hasAnyFilters = availableKeys.isNotEmpty ||
                         availableDifficulties.isNotEmpty ||
                         availableCapos.isNotEmpty ||
                         availableTimeSignatures.isNotEmpty;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Sort by (always show)
        _buildSectionTitle('Sort by'),
        _buildSortOptions(
          options: FilterOptions.songSortOptions,
          selectedOption: _songFilters.sortBy,
          onSelected: (value) {
            setState(() {
              _songFilters.sortBy = value;
            });
          },
        ),
        const SizedBox(height: 16),

        // Show message if no filters are available
        if (!hasAnyFilters) ...[
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[800],
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: Colors.grey[400],
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'No additional filters available for current songs',
                    style: TextStyle(
                      color: Colors.grey[400],
                      fontSize: 14,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ] else ...[
          // Difficulty (only show if there are available options)
          if (availableDifficulties.isNotEmpty) ...[
            _buildSectionTitle('Difficulty (${availableDifficulties.length} available)'),
            _buildFilterOptions(
              options: availableDifficulties,
              selectedOption: _songFilters.difficulty,
              onSelected: (value) {
                setState(() {
                  _songFilters.difficulty = value;
                });
              },
            ),
            const SizedBox(height: 16),
          ],

          // Key (only show if there are available options)
          if (availableKeys.isNotEmpty) ...[
            _buildSectionTitle('Key (${availableKeys.length} available)'),
            _buildFilterOptions(
              options: availableKeys,
              selectedOption: _songFilters.key,
              onSelected: (value) {
                setState(() {
                  _songFilters.key = value;
                });
              },
            ),
            const SizedBox(height: 16),
          ],

          // Capo (only show if there are available options)
          if (availableCapos.isNotEmpty) ...[
            _buildSectionTitle('Capo (${availableCapos.length} available)'),
            _buildFilterOptions(
              options: availableCapos,
              selectedOption: _songFilters.capo?.toString(),
              onSelected: (value) {
                setState(() {
                  _songFilters.capo = value != null ? int.parse(value) : null;
                });
              },
            ),
            const SizedBox(height: 16),
          ],

          // Time Signature (only show if there are available options)
          if (availableTimeSignatures.isNotEmpty) ...[
            _buildSectionTitle('Time Signature (${availableTimeSignatures.length} available)'),
            _buildFilterOptions(
              options: availableTimeSignatures,
              selectedOption: _songFilters.timeSignature,
              onSelected: (value) {
                setState(() {
                  _songFilters.timeSignature = value;
                });
              },
            ),
          ],
        ],
      ],
    );
  }

  Widget _buildArtistFilters() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Sort by
        _buildSectionTitle('Sort by'),
        _buildSortOptions(
          options: FilterOptions.artistSortOptions,
          selectedOption: _artistFilters.sortBy,
          onSelected: (value) {
            setState(() {
              _artistFilters.sortBy = value;
            });
          },
        ),
      ],
    );
  }

  Widget _buildCollectionFilters() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Sort by
        _buildSectionTitle('Sort by'),
        _buildSortOptions(
          options: FilterOptions.collectionSortOptions,
          selectedOption: _collectionFilters.sortBy,
          onSelected: (value) {
            setState(() {
              _collectionFilters.sortBy = value;
            });
          },
        ),
      ],
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Text(
        title,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 16,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildSortOptions({
    required List<FilterOption> options,
    required String? selectedOption,
    required Function(String?) onSelected,
  }) {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: options.map((option) {
        final isSelected = option.id == selectedOption;
        return ChoiceChip(
          label: Text(option.displayName),
          selected: isSelected,
          selectedColor: const Color(0xFFC19FFF), // Light purple/lavender
          backgroundColor: const Color(0xFF333333),
          labelStyle: TextStyle(
            color: isSelected ? Colors.white : Colors.grey,
          ),
          onSelected: (selected) {
            onSelected(selected ? option.id : null);
          },
        );
      }).toList(),
    );
  }

  Widget _buildFilterOptions({
    required List<FilterOption> options,
    required String? selectedOption,
    required Function(String?) onSelected,
  }) {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: options.map((option) {
        final isSelected = option.id == selectedOption;
        return FilterChip(
          label: Text(option.displayName),
          selected: isSelected,
          selectedColor: const Color(0xFFC19FFF), // Light purple/lavender
          backgroundColor: const Color(0xFF333333),
          labelStyle: TextStyle(
            color: isSelected ? Colors.white : Colors.grey,
          ),
          onSelected: (selected) {
            onSelected(selected ? option.id : null);
          },
        );
      }).toList(),
    );
  }

  void _resetFilters() {
    setState(() {
      switch (widget.tabIndex) {
        case 0:
          _songFilters = SongSearchFilters();
          break;
        case 1:
          _artistFilters = ArtistSearchFilters();
          break;
        case 2:
          _collectionFilters = CollectionSearchFilters();
          break;
      }
    });
  }

  void _applyFilters() {
    switch (widget.tabIndex) {
      case 0:
        widget.onSongFiltersApplied(_songFilters);
        break;
      case 1:
        widget.onArtistFiltersApplied(_artistFilters);
        break;
      case 2:
        widget.onCollectionFiltersApplied(_collectionFilters);
        break;
    }
    Navigator.of(context).pop();
  }
}
