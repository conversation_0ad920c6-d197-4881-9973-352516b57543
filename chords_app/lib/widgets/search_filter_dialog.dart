import 'package:flutter/material.dart';
import '../models/search_filters.dart';
import '../models/song.dart';
import '../models/artist.dart';
import '../models/collection.dart';
import '../config/theme.dart';

class SearchFilterDialog extends StatefulWidget {
  final int tabIndex; // 0 = Songs, 1 = Artists, 2 = Collections
  final SongSearchFilters? songFilters;
  final ArtistSearchFilters? artistFilters;
  final CollectionSearchFilters? collectionFilters;
  final Function(SongSearchFilters) onSongFiltersApplied;
  final Function(ArtistSearchFilters) onArtistFiltersApplied;
  final Function(CollectionSearchFilters) onCollectionFiltersApplied;

  // Add data lists to generate dynamic filters
  final List<Song>? availableSongs;
  final List<Artist>? availableArtists;
  final List<Collection>? availableCollections;

  const SearchFilterDialog({
    super.key,
    required this.tabIndex,
    this.songFilters,
    this.artistFilters,
    this.collectionFilters,
    required this.onSongFiltersApplied,
    required this.onArtistFiltersApplied,
    required this.onCollectionFiltersApplied,
    this.availableSongs,
    this.availableArtists,
    this.availableCollections,
  });

  @override
  State<SearchFilterDialog> createState() => _SearchFilterDialogState();
}

class _SearchFilterDialogState extends State<SearchFilterDialog> {
  late SongSearchFilters _songFilters;
  late ArtistSearchFilters _artistFilters;
  late CollectionSearchFilters _collectionFilters;

  @override
  void initState() {
    super.initState();
    _songFilters = widget.songFilters ?? SongSearchFilters();
    _artistFilters = widget.artistFilters ?? ArtistSearchFilters();
    _collectionFilters = widget.collectionFilters ?? CollectionSearchFilters();
  }

  // Generate dynamic filter options based on available data
  List<FilterOption> _getAvailableKeys() {
    if (widget.availableSongs == null || widget.availableSongs!.isEmpty) {
      return [];
    }

    final availableKeys = widget.availableSongs!
        .map((song) => song.key)
        .where((key) => key.isNotEmpty)
        .toSet()
        .toList();

    availableKeys.sort();

    return availableKeys
        .map((key) => FilterOption(id: key, displayName: key))
        .toList();
  }

  List<FilterOption> _getAvailableDifficulties() {
    if (widget.availableSongs == null || widget.availableSongs!.isEmpty) {
      return [];
    }

    final availableDifficulties = widget.availableSongs!
        .map((song) => song.difficulty)
        .where((difficulty) => difficulty != null && difficulty.isNotEmpty)
        .cast<String>()
        .toSet()
        .toList();

    // Sort by difficulty order
    final difficultyOrder = ['BEGINNER', 'EASY', 'MEDIUM', 'HARD', 'EXPERT'];
    availableDifficulties.sort((a, b) {
      final aIndex = difficultyOrder.indexOf(a.toUpperCase());
      final bIndex = difficultyOrder.indexOf(b.toUpperCase());
      if (aIndex == -1 && bIndex == -1) return a.compareTo(b);
      if (aIndex == -1) return 1;
      if (bIndex == -1) return -1;
      return aIndex.compareTo(bIndex);
    });

    return availableDifficulties
        .map((difficulty) => FilterOption(
              id: difficulty.toUpperCase(),
              displayName: _formatDifficultyName(difficulty)
            ))
        .toList();
  }

  List<FilterOption> _getAvailableCapos() {
    if (widget.availableSongs == null || widget.availableSongs!.isEmpty) {
      return [];
    }

    final availableCapos = widget.availableSongs!
        .map((song) => song.capo)
        .where((capo) => capo != null && capo > 0)
        .cast<int>()
        .toSet()
        .toList();

    availableCapos.sort();

    return availableCapos
        .map((capo) => FilterOption(
              id: capo.toString(),
              displayName: 'Capo $capo'
            ))
        .toList();
  }

  List<FilterOption> _getAvailableTimeSignatures() {
    if (widget.availableSongs == null || widget.availableSongs!.isEmpty) {
      return [];
    }

    final availableTimeSignatures = widget.availableSongs!
        .map((song) => song.timeSignature)
        .where((timeSignature) => timeSignature != null && timeSignature.isNotEmpty)
        .cast<String>()
        .toSet()
        .toList();

    availableTimeSignatures.sort();

    return availableTimeSignatures
        .map((timeSignature) => FilterOption(
              id: timeSignature,
              displayName: timeSignature
            ))
        .toList();
  }

  String _formatDifficultyName(String difficulty) {
    switch (difficulty.toUpperCase()) {
      case 'BEGINNER':
        return 'Beginner';
      case 'EASY':
        return 'Easy';
      case 'MEDIUM':
        return 'Medium';
      case 'HARD':
        return 'Hard';
      case 'EXPERT':
        return 'Expert';
      default:
        return difficulty;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: const Color(0xFF1E1E1E),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        constraints: const BoxConstraints(
          maxWidth: 450,
          maxHeight: 700,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Enhanced Header with active filter count
            Container(
              padding: const EdgeInsets.all(20),
              decoration: const BoxDecoration(
                color: Color(0xFF2A2A2A),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.tune,
                    color: const Color(0xFFC19FFF),
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          _getDialogTitle(),
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        if (_getActiveFilterCount() > 0)
                          Text(
                            '${_getActiveFilterCount()} filter${_getActiveFilterCount() == 1 ? '' : 's'} active',
                            style: TextStyle(
                              color: const Color(0xFFC19FFF),
                              fontSize: 12,
                            ),
                          ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(
                      Icons.close,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            ),

            // Filter content with better scrolling
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: _buildFilterContent(),
              ),
            ),

            // Enhanced action buttons
            Container(
              padding: const EdgeInsets.all(20),
              decoration: const BoxDecoration(
                color: Color(0xFF2A2A2A),
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(16),
                  bottomRight: Radius.circular(16),
                ),
              ),
              child: Row(
                children: [
                  // Clear all button (only show if filters are active)
                  if (_getActiveFilterCount() > 0) ...[
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: _resetFilters,
                        icon: const Icon(Icons.clear_all, size: 18),
                        label: const Text('Clear All'),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: Colors.grey,
                          side: const BorderSide(color: Colors.grey),
                          padding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                  ],
                  // Apply button
                  Expanded(
                    flex: 2,
                    child: ElevatedButton.icon(
                      onPressed: _applyFilters,
                      icon: const Icon(Icons.check, size: 18),
                      label: Text(_getActiveFilterCount() > 0
                        ? 'Apply ${_getActiveFilterCount()} Filter${_getActiveFilterCount() == 1 ? '' : 's'}'
                        : 'Apply'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFFC19FFF),
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterContent() {
    switch (widget.tabIndex) {
      case 0:
        return _buildSongFilters();
      case 1:
        return _buildArtistFilters();
      case 2:
        return _buildCollectionFilters();
      default:
        return const SizedBox.shrink();
    }
  }

  Widget _buildSongFilters() {
    // Get dynamic filter options
    final availableKeys = _getAvailableKeys();
    final availableDifficulties = _getAvailableDifficulties();
    final availableCapos = _getAvailableCapos();
    final availableTimeSignatures = _getAvailableTimeSignatures();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Sort by section with enhanced UI
        _buildEnhancedSection(
          title: 'Sort by',
          icon: Icons.sort,
          child: _buildSortOptions(
            options: FilterOptions.songSortOptions,
            selectedOption: _songFilters.sortBy,
            onSelected: (value) {
              setState(() {
                _songFilters.sortBy = value;
              });
            },
          ),
        ),
        const SizedBox(height: 20),

        // Quick filters section
        _buildEnhancedSection(
          title: 'Quick Filters',
          icon: Icons.filter_list,
          child: Column(
            children: [
              // Difficulty filter
              if (availableDifficulties.isNotEmpty) ...[
                _buildFilterRow(
                  label: 'Difficulty',
                  count: availableDifficulties.length,
                  child: _buildFilterOptions(
                    options: availableDifficulties,
                    selectedOption: _songFilters.difficulty,
                    onSelected: (value) {
                      setState(() {
                        _songFilters.difficulty = value;
                      });
                    },
                  ),
                ),
                const SizedBox(height: 16),
              ],

              // Key filter
              if (availableKeys.isNotEmpty) ...[
                _buildFilterRow(
                  label: 'Key',
                  count: availableKeys.length,
                  child: _buildFilterOptions(
                    options: availableKeys,
                    selectedOption: _songFilters.key,
                    onSelected: (value) {
                      setState(() {
                        _songFilters.key = value;
                      });
                    },
                  ),
                ),
                const SizedBox(height: 16),
              ],

              // Capo filter
              if (availableCapos.isNotEmpty) ...[
                _buildFilterRow(
                  label: 'Capo',
                  count: availableCapos.length,
                  child: _buildFilterOptions(
                    options: availableCapos,
                    selectedOption: _songFilters.capo?.toString(),
                    onSelected: (value) {
                      setState(() {
                        _songFilters.capo = value != null ? int.parse(value) : null;
                      });
                    },
                  ),
                ),
                const SizedBox(height: 16),
              ],

              // Time Signature filter
              if (availableTimeSignatures.isNotEmpty) ...[
                _buildFilterRow(
                  label: 'Time Signature',
                  count: availableTimeSignatures.length,
                  child: _buildFilterOptions(
                    options: availableTimeSignatures,
                    selectedOption: _songFilters.timeSignature,
                    onSelected: (value) {
                      setState(() {
                        _songFilters.timeSignature = value;
                      });
                    },
                  ),
                ),
              ],

              // Show message if no filters are available
              if (availableKeys.isEmpty &&
                  availableDifficulties.isEmpty &&
                  availableCapos.isEmpty &&
                  availableTimeSignatures.isEmpty) ...[
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: const Color(0xFF333333),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey[700]!),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        color: Colors.grey[400],
                        size: 20,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          'Search for songs to see available filters',
                          style: TextStyle(
                            color: Colors.grey[400],
                            fontSize: 14,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildArtistFilters() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Sort by section with enhanced UI
        _buildEnhancedSection(
          title: 'Sort Artists',
          icon: Icons.person,
          child: _buildSortOptions(
            options: FilterOptions.artistSortOptions,
            selectedOption: _artistFilters.sortBy,
            onSelected: (value) {
              setState(() {
                _artistFilters.sortBy = value;
              });
            },
          ),
        ),
        const SizedBox(height: 16),

        // Info section for artists
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: const Color(0xFF333333),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey[700]!),
          ),
          child: Row(
            children: [
              Icon(
                Icons.info_outline,
                color: const Color(0xFFC19FFF),
                size: 20,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'Artists are sorted by the selected criteria. More filters coming soon!',
                  style: TextStyle(
                    color: Colors.grey[300],
                    fontSize: 13,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildCollectionFilters() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Sort by section with enhanced UI
        _buildEnhancedSection(
          title: 'Sort Collections',
          icon: Icons.collections_bookmark,
          child: _buildSortOptions(
            options: FilterOptions.collectionSortOptions,
            selectedOption: _collectionFilters.sortBy,
            onSelected: (value) {
              setState(() {
                _collectionFilters.sortBy = value;
              });
            },
          ),
        ),
        const SizedBox(height: 16),

        // Info section for collections
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: const Color(0xFF333333),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey[700]!),
          ),
          child: Row(
            children: [
              Icon(
                Icons.info_outline,
                color: const Color(0xFFC19FFF),
                size: 20,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'Collections are sorted by the selected criteria. Filter by genre and tags coming soon!',
                  style: TextStyle(
                    color: Colors.grey[300],
                    fontSize: 13,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }



  Widget _buildSortOptions({
    required List<FilterOption> options,
    required String? selectedOption,
    required Function(String?) onSelected,
  }) {
    return Wrap(
      spacing: 10,
      runSpacing: 10,
      children: options.map((option) {
        final isSelected = option.id == selectedOption;
        return Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: () => onSelected(isSelected ? null : option.id),
            borderRadius: BorderRadius.circular(20),
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
              decoration: BoxDecoration(
                color: isSelected
                  ? const Color(0xFFC19FFF)
                  : const Color(0xFF444444),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: isSelected
                    ? const Color(0xFFC19FFF)
                    : Colors.grey[600]!,
                  width: 1.5,
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (isSelected) ...[
                    Icon(
                      Icons.check,
                      color: Colors.white,
                      size: 16,
                    ),
                    const SizedBox(width: 6),
                  ],
                  Text(
                    option.displayName,
                    style: TextStyle(
                      color: isSelected ? Colors.white : Colors.grey[300],
                      fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildFilterOptions({
    required List<FilterOption> options,
    required String? selectedOption,
    required Function(String?) onSelected,
  }) {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: options.map((option) {
        final isSelected = option.id == selectedOption;
        return Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: () => onSelected(isSelected ? null : option.id),
            borderRadius: BorderRadius.circular(16),
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 150),
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: isSelected
                  ? const Color(0xFFC19FFF).withValues(alpha: 0.2)
                  : const Color(0xFF444444),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: isSelected
                    ? const Color(0xFFC19FFF)
                    : Colors.grey[600]!,
                  width: 1,
                ),
              ),
              child: Text(
                option.displayName,
                style: TextStyle(
                  color: isSelected ? const Color(0xFFC19FFF) : Colors.grey[300],
                  fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal,
                  fontSize: 13,
                ),
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  void _resetFilters() {
    setState(() {
      switch (widget.tabIndex) {
        case 0:
          _songFilters = SongSearchFilters();
          break;
        case 1:
          _artistFilters = ArtistSearchFilters();
          break;
        case 2:
          _collectionFilters = CollectionSearchFilters();
          break;
      }
    });
  }

  void _applyFilters() {
    switch (widget.tabIndex) {
      case 0:
        widget.onSongFiltersApplied(_songFilters);
        break;
      case 1:
        widget.onArtistFiltersApplied(_artistFilters);
        break;
      case 2:
        widget.onCollectionFiltersApplied(_collectionFilters);
        break;
    }
    Navigator.of(context).pop();
  }

  // Helper method to get dialog title based on current tab
  String _getDialogTitle() {
    switch (widget.tabIndex) {
      case 0:
        return 'Song Filters';
      case 1:
        return 'Artist Filters';
      case 2:
        return 'Collection Filters';
      default:
        return 'Filters';
    }
  }

  // Helper method to count active filters
  int _getActiveFilterCount() {
    switch (widget.tabIndex) {
      case 0:
        int count = 0;
        if (_songFilters.sortBy != null) count++;
        if (_songFilters.difficulty != null) count++;
        if (_songFilters.key != null) count++;
        if (_songFilters.capo != null) count++;
        if (_songFilters.timeSignature != null) count++;
        if (_songFilters.artistId != null) count++;
        if (_songFilters.languageId != null) count++;
        if (_songFilters.tags.isNotEmpty) count++;
        return count;
      case 1:
        return _artistFilters.sortBy != null ? 1 : 0;
      case 2:
        return _collectionFilters.sortBy != null ? 1 : 0;
      default:
        return 0;
    }
  }

  // Enhanced section builder with icons and better styling
  Widget _buildEnhancedSection({
    required String title,
    required IconData icon,
    required Widget child,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF333333),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[700]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                color: const Color(0xFFC19FFF),
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                title,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          child,
        ],
      ),
    );
  }

  // Filter row builder with count indicator
  Widget _buildFilterRow({
    required String label,
    required int count,
    required Widget child,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              label,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(width: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: const Color(0xFFC19FFF).withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Text(
                '$count',
                style: const TextStyle(
                  color: Color(0xFFC19FFF),
                  fontSize: 11,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        child,
      ],
    );
  }
}
