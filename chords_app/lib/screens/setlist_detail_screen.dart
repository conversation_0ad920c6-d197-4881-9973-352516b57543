import 'package:flutter/material.dart';
import '../widgets/inner_screen_app_bar.dart';
import '../widgets/song_placeholder.dart';
import '../models/setlist.dart';
import '../models/song.dart';
import '../services/setlist_service.dart';
import '../services/liked_songs_service.dart';
import '../config/theme.dart';
import 'package:dio/dio.dart';
import 'package:provider/provider.dart';
import '../providers/user_provider.dart';

class SetlistDetailScreen extends StatefulWidget {
  final String setlistId;
  final String setlistName;

  const SetlistDetailScreen({
    super.key,
    required this.setlistId,
    required this.setlistName,
  });

  @override
  State<SetlistDetailScreen> createState() => _SetlistDetailScreenState();
}

class _SetlistDetailScreenState extends State<SetlistDetailScreen> {
  // Removed _currentIndex as we don't need it anymore

  final SetlistService _setlistService = SetlistService();
  final LikedSongsService _likedSongsService = LikedSongsService();
  List<Song> _likedSongs = [];

  Setlist? _setlist;
  bool _isLoading = true;
  bool _isLoggedIn = false;
  bool _isAddingSongs = false;
  List<dynamic> _songs = [];

  @override
  void initState() {
    super.initState();
    debugPrint('SetlistDetailScreen initState for setlist ID: ${widget.setlistId}');

    // First check login status, then fetch setlist details
    _checkLoginStatus().then((_) {
      if (_isLoggedIn) {
        _fetchSetlistDetails();
      }
    });
  }

  Future<void> _checkLoginStatus() async {
    try {
      // Use the UserProvider to check authentication status
      final userProvider = Provider.of<UserProvider>(context, listen: false);
      final isAuthenticated = await userProvider.isAuthenticated();

      setState(() {
        _isLoggedIn = isAuthenticated;
      });

      // If not authenticated, clear any stale data
      if (!isAuthenticated) {
        setState(() {
          _setlist = null;
          _songs = [];
          _likedSongs = [];
        });
      } else {
        // If authenticated, fetch liked songs
        await _fetchLikedSongs();
      }

      debugPrint('Login status checked in detail screen: $_isLoggedIn');
    } catch (e) {
      debugPrint('Error checking login status: $e');
      setState(() {
        _isLoggedIn = false;
        _setlist = null;
        _songs = [];
        _likedSongs = [];
      });
    }
  }

  // Fetch liked songs
  Future<void> _fetchLikedSongs() async {
    debugPrint('Fetching liked songs...');
    try {
      final likedSongs = await _likedSongsService.getLikedSongs();
      setState(() {
        _likedSongs = likedSongs;
      });
      debugPrint('Fetched ${_likedSongs.length} liked songs');
    } catch (e) {
      debugPrint('Error fetching liked songs: $e');
    }
  }

  Future<void> _fetchSetlistDetails() async {
    debugPrint('Fetching setlist details for ID: ${widget.setlistId}');
    setState(() {
      _isLoading = true;
    });

    try {
      // Check if user is logged in
      if (!_isLoggedIn) {
        debugPrint('User not logged in, skipping setlist fetch');
        setState(() {
          _isLoading = false;
        });
        return;
      }

      debugPrint('Calling setlist service to get setlist details');
      final setlist = await _setlistService.getSetlist(widget.setlistId);
      debugPrint('Setlist details received: ${setlist.name}');
      debugPrint('Songs count: ${setlist.songs?.length ?? 0}');

      if (setlist.songs != null) {
        for (var i = 0; i < setlist.songs!.length; i++) {
          debugPrint('Song $i: ${setlist.songs![i]['title'] ?? 'Unknown'}');
        }
      }

      setState(() {
        _setlist = setlist;
        _songs = setlist.songs ?? [];
        _isLoading = false;
      });
      debugPrint('State updated with setlist details');
    } catch (e) {
      debugPrint('Error fetching setlist details: $e');
      setState(() {
        _isLoading = false;
        // If authentication error, update login status
        if (e.toString().contains('Authentication required')) {
          _isLoggedIn = false;
        }
      });

      // Show error message
      if (mounted) {
        final scaffoldMessenger = ScaffoldMessenger.of(context);

        // Check if it's an authentication error
        if (e.toString().contains('Authentication required') ||
            (e is DioException && e.response?.statusCode == 401)) {
          scaffoldMessenger.showSnackBar(
            const SnackBar(
              content: Text('Please log in to view setlists'),
              backgroundColor: Colors.red,
            ),
          );

          // Clear auth state in UserProvider
          final userProvider = Provider.of<UserProvider>(context, listen: false);
          await userProvider.logout(silent: true);

          // Navigate to login screen after a short delay
          Future.delayed(const Duration(seconds: 1), () {
            if (mounted) {
              Navigator.pushReplacementNamed(context, '/login');
            }
          });
        } else {
          scaffoldMessenger.showSnackBar(
            SnackBar(
              content: Text('Failed to load setlist details: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  // Start setlist presentation
  void _startSetlistPresentation() {
    if (_songs.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('No songs in setlist to present'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // Convert songs to the format expected by the presentation screen
    final List<Map<String, dynamic>> songList = [];

    for (var song in _songs) {
      if (song is Map<String, dynamic>) {
        songList.add({
          'id': song['id'] ?? '',
          'title': song['title'] ?? 'Unknown Song',
          'artist': song['artist'] is Map<String, dynamic>
              ? song['artist']['name'] ?? 'Unknown Artist'
              : song['artist'] ?? 'Unknown Artist',
          'lyrics': song['lyrics'],
          'chords': song['chords'],
          'key': song['key'],
          'capo': song['capo'],
          'tempo': song['tempo'],
          'timeSignature': song['timeSignature'],
        });
      }
    }

    // Navigate to setlist presentation screen
    Navigator.pushNamed(
      context,
      '/setlist_presentation',
      arguments: {
        'setlistName': _setlist?.name ?? widget.setlistName,
        'songs': songList,
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF121212),
      appBar: InnerScreenAppBar(
        title: 'Edit Setlist',
        centerTitle: true,
        onBackPressed: () {
          // Return true to indicate that the setlist was modified
          // This will trigger a refresh in the setlist screen
          debugPrint('Navigating back from setlist detail screen with result: true');
          Navigator.of(context).pop(true);
        },
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
              ),
            )
          : !_isLoggedIn
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Text(
                        'Please log in to view setlists',
                        style: TextStyle(color: Colors.white, fontSize: 16),
                      ),
                      const SizedBox(height: 20),
                      ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppTheme.primaryColor,
                          foregroundColor: Colors.black,
                        ),
                        onPressed: () {
                          Navigator.pushReplacementNamed(context, '/login');
                        },
                        child: const Text('Log In'),
                      ),
                    ],
                  ),
                )
              : Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Setlist Title and Action Buttons
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Row(
                    children: [
                      // Setlist Title
                      Expanded(
                        child: Text(
                          _setlist?.name ?? widget.setlistName,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),

                      // Present Button
                      ElevatedButton.icon(
                        icon: const Icon(
                          Icons.present_to_all,
                          size: 20,
                        ),
                        label: const Text(
                          'Present',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                          ),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: _songs.isEmpty
                              ? Colors.grey.withValues(alpha: 0.3)
                              : AppTheme.primaryColor,
                          foregroundColor: _songs.isEmpty
                              ? Colors.grey
                              : Colors.black,
                          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(20),
                          ),
                          elevation: _songs.isEmpty ? 0 : 2,
                        ),
                        onPressed: _songs.isEmpty ? null : () {
                          // Navigate to setlist presentation
                          _startSetlistPresentation();
                        },
                      ),

                      // Share Button
                      IconButton(
                        icon: const Icon(
                          Icons.share,
                          color: Colors.white,
                        ),
                        onPressed: () {
                          // Share functionality
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('Share functionality coming soon!'),
                              backgroundColor: Color(0xFF1E1E1E),
                            ),
                          );
                        },
                      ),

                      // Edit Button
                      IconButton(
                        icon: const Icon(
                          Icons.edit,
                          color: Colors.white,
                        ),
                        onPressed: () {
                          // Edit functionality
                          _showEditSetlistDialog();
                        },
                      ),
                    ],
                  ),
                ),

                // Description
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  child: Text(
                    _setlist?.description ?? 'No description',
                    style: const TextStyle(
                      color: Colors.grey,
                      fontSize: 14,
                    ),
                  ),
                ),

                const Divider(
                  color: Color(0xFF333333),
                  height: 32,
                ),

                // Songs List
                Expanded(
                  child: _songs.isEmpty
                      ? const Center(
                          child: Text(
                            'No songs in this setlist yet',
                            style: TextStyle(color: Colors.white),
                          ),
                        )
                      : RefreshIndicator(
                          onRefresh: () async {
                            // Force refresh setlist details
                            await _fetchSetlistDetails();
                          },
                          color: AppTheme.primaryColor,
                          child: ListView.builder(
                            itemCount: _songs.length,
                            itemBuilder: (context, index) {
                              final song = _songs[index];
                              debugPrint('Building song item for index $index: ${song.toString()}');

                              // Extract song data safely
                              String title = 'Unknown Song';
                              String artist = 'Unknown Artist';
                              String songId = '';

                              try {
                                if (song is Map<String, dynamic>) {
                                  title = song['title'] ?? 'Unknown Song';

                                  // Handle artist data which could be a string or a map
                                  if (song['artist'] is Map<String, dynamic>) {
                                    artist = song['artist']['name'] ?? 'Unknown Artist';
                                  } else if (song['artist'] is String) {
                                    artist = song['artist'];
                                  }

                                  songId = song['id'] ?? '';
                                  debugPrint('Extracted song data - Title: $title, Artist: $artist, ID: $songId');
                                }
                              } catch (e) {
                                debugPrint('Error extracting song data: $e');
                              }

                              return _buildSongItem(title, artist, songId);
                            },
                          ),
                        ),
                ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        backgroundColor: Colors.white,
        child: const Icon(
          Icons.add,
          color: Colors.black,
        ),
        onPressed: () {
          // Add new song to setlist
          _showAddSongDialog();
        },
      ),
      // Bottom navigation bar removed from inner screens
    );
  }

  Widget _buildSongItem(String title, String artist, String songId) {
    // Get the song placeholder size
    const double placeholderSize = 48.0;

    return Container(
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: Color(0xFF333333),
            width: 1.0,
          ),
        ),
      ),
      child: ListTile(
        // Reduce vertical padding to decrease space between items
        contentPadding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 4.0),
        leading: const SongPlaceholder(size: placeholderSize),
        title: Text(
          title,
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
          // Ensure text doesn't wrap unnecessarily
          overflow: TextOverflow.ellipsis,
        ),
        subtitle: Text(
          artist,
          style: const TextStyle(
            color: Colors.grey,
          ),
          overflow: TextOverflow.ellipsis,
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Delete button
            IconButton(
              icon: const Icon(
                Icons.delete_outline,
                color: Colors.grey,
                size: 20,
              ),
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(),
              onPressed: () {
                // Delete song from setlist
                _showRemoveSongDialog(songId, title);
              },
            ),
          ],
        ),
        onTap: () {
          // Navigate to song detail or chord sheet
          Navigator.pushNamed(
            context,
            '/song_detail',
            arguments: {
              'songId': songId,
              'songTitle': title,
            },
          );
        },
      ),
    );
  }

  void _showAddSongDialog() {
    // Check if there are liked songs
    if (_likedSongs.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('You have no liked songs. Like some songs first!'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // Create a set of song IDs that are already in the setlist
    final Set<String> existingSongIds = {};

    // Extract song IDs from the setlist songs
    for (var song in _songs) {
      if (song is Map<String, dynamic> && song['id'] != null) {
        existingSongIds.add(song['id']);
      }
    }

    // Set of selected song IDs for multi-select (initially empty)
    final Set<String> selectedSongIds = {};

    // Show bottom sheet instead of dialog for better scrolling with long lists
    showModalBottomSheet(
      context: context,
      isScrollControlled: true, // Makes the bottom sheet take up the full screen
      backgroundColor: const Color(0xFF1E1E1E),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (BuildContext context) {
        // Calculate available height (80% of screen height)
        final availableHeight = MediaQuery.of(context).size.height * 0.8;

        return StatefulBuilder(
          builder: (context, setState) {
            return Container(
              constraints: BoxConstraints(
                maxHeight: availableHeight,
              ),
              padding: EdgeInsets.only(
                left: 16.0,
                right: 16.0,
                top: 16.0,
                bottom: 16.0 + MediaQuery.of(context).padding.bottom, // Add safe area bottom padding
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header with close button and selection count
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Add Songs to Setlist',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          if (selectedSongIds.isNotEmpty)
                            Text(
                              '${selectedSongIds.length} selected',
                              style: const TextStyle(
                                color: AppTheme.primaryColor,
                                fontSize: 14,
                              ),
                            ),
                        ],
                      ),
                      IconButton(
                        icon: const Icon(Icons.close, color: Colors.white),
                        onPressed: () => Navigator.pop(context),
                      ),
                    ],
                  ),

                  const SizedBox(height: 8),

                  // Instructions
                  const Text(
                    'Select songs from your liked songs:',
                    style: TextStyle(color: Colors.white70),
                  ),

                  const SizedBox(height: 8),

                  // Song list (scrollable) - More compact
                  Expanded(
                    child: ListView.builder(
                      itemCount: _likedSongs.length,
                      itemBuilder: (context, index) {
                        final song = _likedSongs[index];
                        final bool isInSetlist = existingSongIds.contains(song.id);

                        // Initialize selection state - if song is already in setlist, it's pre-selected
                        if (isInSetlist && !selectedSongIds.contains(song.id)) {
                          // Add to selected songs if it's the first time rendering and song is in setlist
                          selectedSongIds.add(song.id);
                        }

                        final bool isSelected = selectedSongIds.contains(song.id);

                        return Container(
                          decoration: BoxDecoration(
                            border: index < _likedSongs.length - 1
                                ? const Border(
                                    bottom: BorderSide(
                                      color: Color(0xFF333333),
                                      width: 0.5,
                                    ),
                                  )
                                : null,
                            color: isInSetlist ? const Color(0xFF252525) : null, // Subtle background for existing songs
                          ),
                          child: ListTile(
                            dense: true, // Makes the list tile more compact
                            contentPadding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 0.0),
                            title: Row(
                              children: [
                                Expanded(
                                  child: Text(
                                    song.title,
                                    style: TextStyle(
                                      color: isSelected ? AppTheme.primaryColor : Colors.white,
                                      fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                                      fontSize: 15, // Slightly smaller font
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                                if (isInSetlist)
                                  const Padding(
                                    padding: EdgeInsets.only(left: 4.0),
                                    child: Icon(
                                      Icons.playlist_add_check,
                                      color: AppTheme.primaryColor,
                                      size: 16,
                                    ),
                                  ),
                              ],
                            ),
                            subtitle: Text(
                              song.artist,
                              style: TextStyle(
                                color: isSelected ? Colors.white70 : Colors.grey,
                                fontSize: 13, // Smaller font for subtitle
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                            trailing: Checkbox(
                              value: isSelected,
                              activeColor: AppTheme.primaryColor,
                              checkColor: Colors.black,
                              side: const BorderSide(color: Colors.grey),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(4),
                              ),
                              onChanged: (bool? value) {
                                setState(() {
                                  if (value == true) {
                                    selectedSongIds.add(song.id);
                                  } else {
                                    selectedSongIds.remove(song.id);
                                  }
                                });
                              },
                            ),
                            onTap: () {
                              setState(() {
                                if (selectedSongIds.contains(song.id)) {
                                  selectedSongIds.remove(song.id);
                                } else {
                                  selectedSongIds.add(song.id);
                                }
                              });
                            },
                          ),
                        );
                      },
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Action buttons
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // Select all button
                      TextButton.icon(
                        icon: const Icon(Icons.select_all, color: Colors.grey),
                        label: Text(
                          selectedSongIds.length == _likedSongs.length ? 'Deselect All' : 'Select All',
                          style: const TextStyle(color: Colors.grey),
                        ),
                        onPressed: () {
                          setState(() {
                            if (selectedSongIds.length == _likedSongs.length) {
                              // If all are selected, deselect all except those already in setlist
                              selectedSongIds.clear();
                              // Re-add songs that are already in the setlist
                              for (var song in _likedSongs) {
                                if (existingSongIds.contains(song.id)) {
                                  selectedSongIds.add(song.id);
                                }
                              }
                            } else {
                              // Otherwise, select all
                              selectedSongIds.clear();
                              for (var song in _likedSongs) {
                                selectedSongIds.add(song.id);
                              }
                            }
                          });
                        },
                      ),

                      Row(
                        children: [
                          // Cancel button
                          TextButton(
                            child: const Text(
                              'Cancel',
                              style: TextStyle(color: Colors.grey),
                            ),
                            onPressed: () {
                              Navigator.of(context).pop();
                            },
                          ),
                          const SizedBox(width: 8),
                          // Add button
                          ElevatedButton(
                            style: ElevatedButton.styleFrom(
                              backgroundColor: _isAddingSongs ? Colors.grey : AppTheme.primaryColor,
                              foregroundColor: Colors.black,
                              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            onPressed: _isAddingSongs ? null : () async {
                              // Add songs to setlist logic
                              if (selectedSongIds.isNotEmpty) {
                                // Filter out songs that are already in the setlist
                                final Set<String> newSongIds = selectedSongIds.difference(existingSongIds);

                                // If no new songs to add, show message and return
                                if (newSongIds.isEmpty) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                      content: Text('All selected songs are already in the setlist'),
                                      backgroundColor: Colors.orange,
                                    ),
                                  );
                                  return;
                                }

                                Navigator.of(context).pop();

                                // Set loading state
                                setState(() {
                                  _isAddingSongs = true;
                                });

                                // Store context before async operations
                                final scaffoldMessenger = ScaffoldMessenger.of(context);

                                // Optimistic UI update - add songs to local state immediately
                                final List<Song> songsToAdd = _likedSongs.where((song) => newSongIds.contains(song.id)).toList();

                                setState(() {
                                  // Add new songs to the local setlist
                                  for (var song in songsToAdd) {
                                    _songs.add({
                                      'id': song.id,
                                      'title': song.title,
                                      'artist': {
                                        'name': song.artist,
                                      },
                                      'key': song.key,
                                      'capo': song.capo,
                                      'tempo': song.tempo,
                                      'timeSignature': song.timeSignature,
                                      'lyrics': song.lyrics,
                                      'chords': song.chords,
                                    });
                                  }
                                });

                                try {
                                  // Use bulk add API for much faster performance
                                  await _setlistService.addMultipleSongsToSetlist(widget.setlistId, newSongIds.toList());

                                  // Reset loading state
                                  setState(() {
                                    _isAddingSongs = false;
                                  });

                                  // Show success message
                                  if (mounted) {
                                    scaffoldMessenger.showSnackBar(
                                      SnackBar(
                                        content: Text('Added ${newSongIds.length} song${newSongIds.length > 1 ? "s" : ""} to setlist'),
                                        backgroundColor: Colors.green,
                                        duration: const Duration(seconds: 2),
                                      ),
                                    );
                                  }

                                  // Refresh setlist details to ensure consistency
                                  await _fetchSetlistDetails();
                                } catch (e) {
                                  debugPrint('Error adding songs to setlist: $e');

                                  // Reset loading state and revert optimistic update on error
                                  setState(() {
                                    _isAddingSongs = false;
                                    for (var song in songsToAdd) {
                                      _songs.removeWhere((s) => s['id'] == song.id);
                                    }
                                  });

                                  // Show error message
                                  if (mounted) {
                                    scaffoldMessenger.showSnackBar(
                                      SnackBar(
                                        content: Text('Failed to add songs to setlist: ${e.toString().replaceAll('Exception: ', '')}'),
                                        backgroundColor: Colors.red,
                                        duration: const Duration(seconds: 3),
                                      ),
                                    );
                                  }
                                }
                              } else {
                                // Show error
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text('Please select at least one song to add'),
                                    backgroundColor: Colors.red,
                                  ),
                                );
                              }
                            },
                            child: _isAddingSongs
                              ? const SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(Colors.black),
                                  ),
                                )
                              : Text(
                                  'Add ${selectedSongIds.isNotEmpty ? "(${selectedSongIds.length})" : ""}',
                                  style: const TextStyle(fontWeight: FontWeight.bold),
                                ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  void _showEditSetlistDialog() {
    String newName = _setlist?.name ?? widget.setlistName;
    String newDescription = _setlist?.description ?? '';

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFF1E1E1E),
          title: const Text(
            'Edit Setlist',
            style: TextStyle(color: Colors.white),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                style: const TextStyle(color: Colors.white),
                decoration: const InputDecoration(
                  hintText: 'Setlist Name',
                  hintStyle: TextStyle(color: Colors.grey),
                  enabledBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey),
                  ),
                  focusedBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: AppTheme.primaryColor),
                  ),
                ),
                controller: TextEditingController(text: newName),
                onChanged: (value) {
                  newName = value;
                },
              ),
              const SizedBox(height: 16),
              TextField(
                style: const TextStyle(color: Colors.white),
                decoration: const InputDecoration(
                  hintText: 'Description (Optional)',
                  hintStyle: TextStyle(color: Colors.grey),
                  enabledBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey),
                  ),
                  focusedBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: AppTheme.primaryColor),
                  ),
                ),
                controller: TextEditingController(text: newDescription),
                onChanged: (value) {
                  newDescription = value;
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              child: const Text(
                'Cancel',
                style: TextStyle(color: Colors.grey),
              ),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: const Text(
                'Save',
                style: TextStyle(color: AppTheme.primaryColor),
              ),
              onPressed: () async {
                if (newName.isNotEmpty) {
                  Navigator.of(context).pop();

                  // Show loading indicator
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Updating setlist...'),
                      duration: Duration(seconds: 1),
                      backgroundColor: Color(0xFF1E1E1E),
                    ),
                  );

                  try {
                    await _setlistService.updateSetlist(
                      widget.setlistId,
                      newName,
                      description: newDescription.isNotEmpty ? newDescription : null,
                    );

                    // Refresh setlist details
                    await _fetchSetlistDetails();

                    // Show success message
                    if (mounted) {
                      // Store context in a local variable
                      final currentContext = context;
                      // Use a post-frame callback to avoid BuildContext issues
                      WidgetsBinding.instance.addPostFrameCallback((_) {
                        if (mounted) {
                          ScaffoldMessenger.of(currentContext).showSnackBar(
                            const SnackBar(
                              content: Text('Setlist updated successfully'),
                              backgroundColor: Colors.green,
                            ),
                          );
                        }
                      });
                    }
                  } catch (e) {
                    // Show error message
                    if (mounted) {
                      // Store context in a local variable
                      final currentContext = context;
                      // Use a post-frame callback to avoid BuildContext issues
                      WidgetsBinding.instance.addPostFrameCallback((_) {
                        if (mounted) {
                          ScaffoldMessenger.of(currentContext).showSnackBar(
                            SnackBar(
                              content: Text('Failed to update setlist: $e'),
                              backgroundColor: Colors.red,
                            ),
                          );
                        }
                      });
                    }
                  }
                } else {
                  // Show error for empty name
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Setlist name cannot be empty'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              },
            ),
          ],
        );
      },
    );
  }

  void _showRemoveSongDialog(String songId, String songTitle) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFF1E1E1E),
          title: const Text(
            'Remove Song',
            style: TextStyle(color: Colors.white),
          ),
          content: Text(
            'Are you sure you want to remove "$songTitle" from this setlist?',
            style: const TextStyle(color: Colors.white),
          ),
          actions: [
            TextButton(
              child: const Text(
                'Cancel',
                style: TextStyle(color: Colors.grey),
              ),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: const Text(
                'Remove',
                style: TextStyle(color: Colors.red),
              ),
              onPressed: () async {
                Navigator.of(context).pop();

                // Show loading indicator
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Removing song from setlist...'),
                    duration: Duration(seconds: 1),
                    backgroundColor: Color(0xFF1E1E1E),
                  ),
                );

                try {
                  await _setlistService.removeSongFromSetlist(widget.setlistId, songId);

                  // Refresh setlist details
                  await _fetchSetlistDetails();

                  // Show success message
                  if (mounted) {
                    // Store context in a local variable
                    final currentContext = context;
                    // Use a post-frame callback to avoid BuildContext issues
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      if (mounted) {
                        ScaffoldMessenger.of(currentContext).showSnackBar(
                          SnackBar(
                            content: Text('Song "$songTitle" removed from setlist'),
                            backgroundColor: Colors.green,
                          ),
                        );
                      }
                    });
                  }
                } catch (e) {
                  // Show error message
                  if (mounted) {
                    // Store context in a local variable
                    final currentContext = context;
                    // Use a post-frame callback to avoid BuildContext issues
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      if (mounted) {
                        ScaffoldMessenger.of(currentContext).showSnackBar(
                          SnackBar(
                            content: Text('Failed to remove song: $e'),
                            backgroundColor: Colors.red,
                          ),
                        );
                      }
                    });
                  }
                }
              },
            ),
          ],
        );
      },
    );
  }
}
