import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/services.dart';
import 'package:flutter/material.dart' show debugPrint;
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import '../models/song.dart';
import '../services/permission_service.dart';

class PdfService {
  // Generate a PDF for a song
  Future<Uint8List> generateSongPdf(Song song, {bool showChords = true}) async {
    // Create a PDF document
    final pdf = pw.Document();

    // No custom theme needed, using default fonts

    // Create a PDF page
    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(32),
        header: (context) => _buildHeader(context, song),
        footer: (context) => _buildFooter(context),
        build: (context) => [
          _buildSongDetails(song),
          pw.SizedBox(height: 20),
          _buildChordSheet(song, showChords),
        ],
      ),
    );

    // Return the PDF document as bytes
    return pdf.save();
  }

  // Build the header of the PDF
  pw.Widget _buildHeader(pw.Context context, Song song) {
    return pw.Container(
      alignment: pw.Alignment.center,
      margin: const pw.EdgeInsets.only(bottom: 20),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.center,
        children: [
          pw.Text(
            song.title,
            style: pw.TextStyle(
              fontSize: 24,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
          pw.SizedBox(height: 4),
          pw.Text(
            song.artist,
            style: pw.TextStyle(
              fontSize: 16,
              fontStyle: pw.FontStyle.italic,
            ),
          ),
        ],
      ),
    );
  }

  // Build the footer of the PDF
  pw.Widget _buildFooter(pw.Context context) {
    return pw.Container(
      alignment: pw.Alignment.centerRight,
      margin: const pw.EdgeInsets.only(top: 20),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Text(
            'Generated by Stuthi',
            style: pw.TextStyle(
              fontSize: 10,
              color: PdfColors.grey700,
            ),
          ),
          pw.Text(
            'Page ${context.pageNumber} of ${context.pagesCount}',
            style: pw.TextStyle(
              fontSize: 10,
              color: PdfColors.grey700,
            ),
          ),
        ],
      ),
    );
  }

  // Build the song details section
  pw.Widget _buildSongDetails(Song song) {
    // Create a list of details to display
    final List<Map<String, String>> details = [];

    // Add key
    details.add({'label': 'Key', 'value': song.key});

    // Add capo if available
    if (song.capo != null && song.capo! > 0) {
      details.add({'label': 'Capo', 'value': song.capo.toString()});
    }

    // Add time signature if available
    if (song.timeSignature != null && song.timeSignature!.isNotEmpty) {
      details.add({'label': 'Time', 'value': song.timeSignature!});
    }

    // Add tempo if available
    if (song.tempo != null && song.tempo! > 0) {
      details.add({'label': 'Tempo', 'value': '${song.tempo} BPM'});
    }

    // Build the details section
    return pw.Container(
      padding: const pw.EdgeInsets.all(10),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.grey300),
        borderRadius: const pw.BorderRadius.all(pw.Radius.circular(8)),
      ),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceAround,
        children: details.map((detail) {
          return pw.Column(
            children: [
              pw.Text(
                detail['label']!,
                style: pw.TextStyle(
                  fontSize: 12,
                  color: PdfColors.grey700,
                ),
              ),
              pw.SizedBox(height: 4),
              pw.Text(
                detail['value']!,
                style: const pw.TextStyle(
                  fontSize: 14,
                ),
              ),
            ],
          );
        }).toList(),
      ),
    );
  }

  // Build the chord sheet section
  pw.Widget _buildChordSheet(Song song, bool showChords) {
    if (song.chords == null || song.chords!.isEmpty) {
      return pw.Center(
        child: pw.Text(
          'No chord sheet available',
          style: pw.TextStyle(
            fontSize: 14,
            color: PdfColors.grey,
          ),
        ),
      );
    }

    // Process the chord sheet
    final lines = song.chords!.split('\n');

    // Identify chord lines and their corresponding lyric lines
    List<Map<String, dynamic>> processedLines = [];
    for (int i = 0; i < lines.length; i++) {
      final line = lines[i];
      final isChordLine = _isChordLine(line);
      final bool isSection = line.trim().startsWith('[') && line.trim().endsWith(']') &&
                            !RegExp(r'\[[A-G][#b]?.*?\]').hasMatch(line); // Make sure it's not a chord in brackets

      // Add the line with its type
      processedLines.add({
        'text': line,
        'isChordLine': isChordLine,
        'isSection': isSection,
      });
    }

    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: processedLines.map((lineData) {
        final String line = lineData['text'];
        final bool isChordLine = lineData['isChordLine'];
        final bool isSection = lineData['isSection'];

        // Handle section headers
        if (isSection) {
          return pw.Padding(
            padding: const pw.EdgeInsets.only(top: 12, bottom: 4),
            child: pw.Text(
              line.trim(),
              style: pw.TextStyle(
                fontSize: 14,
                fontWeight: pw.FontWeight.bold,
                color: PdfColors.grey700,
              ),
            ),
          );
        }

        // Skip chord lines if showChords is false
        if (isChordLine && !showChords) {
          return pw.Container(height: 0);
        }

        // If not showing chords, remove inline chord markers [C], [Am], etc.
        String displayText = line;
        if (!showChords) {
          // Remove chord markers like [C], [Am], etc.
          displayText = line.replaceAll(RegExp(r'\[[A-G][#b]?.*?\]'), '');
        }

        // Regular line (chord or lyric)
        return pw.Padding(
          padding: const pw.EdgeInsets.symmetric(vertical: 2),
          child: pw.Text(
            displayText,
            style: pw.TextStyle(
              fontSize: 12,
              fontWeight: isChordLine ? pw.FontWeight.bold : pw.FontWeight.normal,
              color: isChordLine ? PdfColors.blue800 : PdfColors.black,
            ),
          ),
        );
      }).toList(),
    );
  }

  // Helper method to check if a line is a chord line
  bool _isChordLine(String line) {
    // Common chord patterns
    final chordPattern = RegExp(r'[A-G][#b]?(m|maj|min|dim|aug|sus|add|maj7|m7|7|9|11|13)?');

    // Check if the line contains chords in brackets like [C] [Am] etc.
    if (RegExp(r'\[[A-G][#b]?.*?\]').hasMatch(line)) {
      return true;
    }

    // Characteristics of chord lines:
    // 1. Contains recognizable chord patterns
    // 2. Usually has more spaces than a typical lyric line
    // 3. Often shorter than lyric lines

    // Count spaces and non-spaces
    int spaces = 0;
    int nonSpaces = 0;
    for (int i = 0; i < line.length; i++) {
      if (line[i] == ' ') {
        spaces++;
      } else {
        nonSpaces++;
      }
    }

    // If the line is empty or just whitespace, it's not a chord line
    if (nonSpaces == 0) return false;

    // If the line has a high ratio of spaces to characters, it's likely a chord line
    final spaceRatio = spaces / (spaces + nonSpaces);

    // Check if the line contains chord patterns
    final containsChords = chordPattern.hasMatch(line);

    // Combine heuristics
    return containsChords && (spaceRatio > 0.3 || line.length < 20);
  }

  // Print the PDF
  Future<void> printSongPdf(Song song, {bool showChords = true}) async {
    final pdfData = await generateSongPdf(song, showChords: showChords);
    await Printing.layoutPdf(
      onLayout: (format) => pdfData,
      name: '${song.title} - ${song.artist}',
    );
  }

  // Save the PDF to a file and return the file path
  Future<String> saveSongPdf(Song song, {bool showChords = true}) async {
    final pdfData = await generateSongPdf(song, showChords: showChords);

    // Create a safe filename
    final safeTitle = song.title.replaceAll(RegExp(r'[^\w\s]+'), '').replaceAll(' ', '_');
    final safeArtist = song.artist.replaceAll(RegExp(r'[^\w\s]+'), '').replaceAll(' ', '_');
    final filename = '${safeTitle}_$safeArtist.pdf';

    // Get the appropriate directory for saving files
    String filePath;

    try {
      if (Platform.isAndroid) {
        // For Android, we'll use a simpler approach that's more likely to work
        // First, check if we have storage permission
        final permissionService = PermissionService();
        final hasPermission = await permissionService.requestStoragePermission();

        if (!hasPermission) {
          debugPrint('Storage permission denied, falling back to app directory');
          // Fall back to app's cache directory if permission is denied
          final cacheDir = await getTemporaryDirectory();
          filePath = '${cacheDir.path}/$filename';
        } else {
          // Try to use the external storage directory
          final externalDir = await getExternalStorageDirectory();
          if (externalDir != null) {
            // Create a Downloads folder in the app's external storage
            final downloadsDir = Directory('${externalDir.path}/Download');
            if (!await downloadsDir.exists()) {
              await downloadsDir.create(recursive: true);
            }
            filePath = '${downloadsDir.path}/$filename';
            debugPrint('Using app external storage: $filePath');
          } else {
            // Fall back to the app's documents directory
            final docsDir = await getApplicationDocumentsDirectory();
            filePath = '${docsDir.path}/$filename';
            debugPrint('Using app documents directory: $filePath');
          }
        }
      } else if (Platform.isIOS) {
        // For iOS, use the documents directory
        final docsDir = await getApplicationDocumentsDirectory();
        filePath = '${docsDir.path}/$filename';
        debugPrint('Using iOS documents directory: $filePath');
      } else {
        // For other platforms, use the documents directory
        final docsDir = await getApplicationDocumentsDirectory();
        filePath = '${docsDir.path}/$filename';
        debugPrint('Using documents directory: $filePath');
      }

      // Create the file
      final file = File(filePath);
      await file.writeAsBytes(pdfData);

      debugPrint('PDF saved successfully to: $filePath');
      return filePath;

    } catch (e) {
      debugPrint('Error saving PDF: $e');
      // If there's an error, fall back to the temporary directory
      final tempDir = await getTemporaryDirectory();
      filePath = '${tempDir.path}/$filename';

      final file = File(filePath);
      await file.writeAsBytes(pdfData);

      debugPrint('PDF saved to temporary location: $filePath');
      return filePath;
    }
  }

  // Share the PDF
  Future<void> shareSongPdf(Song song, {bool showChords = true}) async {
    final filePath = await saveSongPdf(song, showChords: showChords);

    // Share the file
    await Share.shareXFiles(
      [XFile(filePath)],
      text: 'Chord sheet for ${song.title} by ${song.artist}',
      subject: '${song.title} - Chord Sheet',
    );
  }
}
