import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/setlist.dart';
import 'api_service.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class SetlistService {
  final ApiService _apiService = ApiService();
  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage();

  // Check if user is authenticated
  Future<bool> isAuthenticated() async {
    try {
      // First check for access token
      final token = await _secureStorage.read(key: 'access_token');
      if (token != null) {
        debugPrint('Access token found, user is authenticated');
        return true;
      }

      // If no access token, check Firebase auth
      final firebaseUser = FirebaseAuth.instance.currentUser;
      if (firebaseUser != null) {
        try {
          // Get a fresh token
          final idToken = await firebaseUser.getIdToken(true);
          debugPrint('Firebase user is authenticated, got fresh token');

          // Store the token for future use
          await _secureStorage.write(key: 'firebase_token', value: idToken);

          return true;
        } catch (e) {
          debugPrint('Error getting Firebase token: $e');
          return false;
        }
      }

      debugPrint('No authentication found (no token, no Firebase user)');
      return false;
    } catch (e) {
      debugPrint('Error checking authentication: $e');
      return false;
    }
  }

  // Get all setlists for the current user
  Future<List<Setlist>> getSetlists() async {
    try {
      // Check if user is authenticated
      if (!await isAuthenticated()) {
        debugPrint('User is not authenticated when fetching setlists');
        throw Exception('Authentication required. Please log in.');
      }

      // The API service will automatically use the token from secure storage
      // or get a fresh Firebase token if needed
      debugPrint('Fetching setlists from API');

      // Make the API request
      final response = await _apiService.get('/setlists');

      if (response.statusCode == 200) {
        final List<dynamic> data = response.data;
        debugPrint('Received ${data.length} setlists from API');

        // Log the raw data for debugging
        debugPrint('Raw setlist data: $data');

        if (data.isEmpty) {
          debugPrint('No setlists found for the current customer');
          return [];
        }

        try {
          final setlists = data.map((json) => Setlist.fromJson(json)).toList();
          debugPrint('Parsed ${setlists.length} setlists');

          // Log each setlist for debugging
          for (var setlist in setlists) {
            debugPrint('Setlist: ${setlist.id} - ${setlist.name}');
          }

          return setlists;
        } catch (parseError) {
          debugPrint('Error parsing setlist data: $parseError');
          throw Exception('Failed to parse setlists: $parseError');
        }
      } else {
        debugPrint('Failed to load setlists: ${response.statusCode}');
        throw Exception('Failed to load setlists: Status ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error getting setlists: $e');
      if (e is DioException) {
        debugPrint('DioException status code: ${e.response?.statusCode}');
        debugPrint('DioException response data: ${e.response?.data}');

        if (e.response?.statusCode == 401) {
          // Clear tokens on authentication error
          await _secureStorage.delete(key: 'access_token');
          await _secureStorage.delete(key: 'refresh_token');
          throw Exception('Authentication required. Please log in.');
        }
      }
      throw Exception('Failed to load setlists: $e');
    }
  }

  // Create a new setlist
  Future<Setlist> createSetlist(String name, {String? description}) async {
    try {
      // Check if user is authenticated
      if (!await isAuthenticated()) {
        throw Exception('Authentication required. Please log in.');
      }

      // The API service will automatically use the token from secure storage
      // or get a fresh Firebase token if needed
      debugPrint('Creating setlist: $name');

      // Make the API request
      final response = await _apiService.post('/setlists', data: {
        'name': name,
        'description': description,
      });

      if (response.statusCode == 201) {
        debugPrint('Setlist created successfully: ${response.data}');
        try {
          final setlist = Setlist.fromJson(response.data);
          debugPrint('Parsed setlist: ${setlist.id} - ${setlist.name}');
          return setlist;
        } catch (parseError) {
          debugPrint('Error parsing created setlist: $parseError');
          throw Exception('Failed to parse created setlist: $parseError');
        }
      } else {
        debugPrint('Failed to create setlist: ${response.statusCode}');
        throw Exception('Failed to create setlist: Status ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error creating setlist: $e');
      if (e is DioException) {
        debugPrint('DioException status code: ${e.response?.statusCode}');
        debugPrint('DioException response data: ${e.response?.data}');

        if (e.response?.statusCode == 401) {
          // Clear tokens on authentication error
          await _secureStorage.delete(key: 'access_token');
          await _secureStorage.delete(key: 'refresh_token');
          throw Exception('Authentication required. Please log in.');
        }
      }
      throw Exception('Failed to create setlist: $e');
    }
  }

  // Get a specific setlist by ID
  Future<Setlist> getSetlist(String id) async {
    try {
      // Check if user is authenticated
      if (!await isAuthenticated()) {
        debugPrint('User not authenticated when fetching setlist $id');
        throw Exception('Authentication required. Please log in.');
      }

      debugPrint('Sending API request to get setlist $id');
      final response = await _apiService.get('/setlists/$id');

      if (response.statusCode == 200) {
        debugPrint('Setlist $id fetched successfully');
        debugPrint('Response data: ${response.data}');

        // Check if response data is valid
        if (response.data == null) {
          debugPrint('Response data is null');
          throw Exception('Invalid setlist data received');
        }

        // Log songs data if available
        if (response.data['songs'] != null) {
          debugPrint('Songs in response: ${response.data['songs'].length}');
          for (var song in response.data['songs']) {
            debugPrint('Song: ${song['title'] ?? 'Unknown'} by ${song['artist']?['name'] ?? 'Unknown Artist'}');
          }
        } else {
          debugPrint('No songs in response');
        }

        return Setlist.fromJson(response.data);
      } else {
        debugPrint('Failed to load setlist: ${response.statusCode}');
        throw Exception('Failed to load setlist: Status ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error getting setlist: $e');
      if (e is DioException) {
        debugPrint('DioException status code: ${e.response?.statusCode}');
        debugPrint('DioException response data: ${e.response?.data}');

        if (e.response?.statusCode == 401) {
          // Clear tokens on authentication error
          await _secureStorage.delete(key: 'access_token');
          await _secureStorage.delete(key: 'refresh_token');
          throw Exception('Authentication required. Please log in.');
        }
      }
      throw Exception('Failed to load setlist: $e');
    }
  }

  // Update a setlist
  Future<Setlist> updateSetlist(String id, String name, {String? description}) async {
    try {
      // Check if user is authenticated
      if (!await isAuthenticated()) {
        throw Exception('Authentication required. Please log in.');
      }

      debugPrint('Updating setlist with ID: $id, name: $name, description: $description');
      final response = await _apiService.patch('/setlists/$id', data: {
        'name': name,
        'description': description,
      });

      if (response.statusCode == 200) {
        return Setlist.fromJson(response.data);
      } else {
        throw Exception('Failed to update setlist');
      }
    } catch (e) {
      debugPrint('Error updating setlist: $e');
      if (e is DioException && e.response?.statusCode == 401) {
        throw Exception('Authentication required. Please log in.');
      }
      throw Exception('Failed to update setlist: $e');
    }
  }

  // Delete a setlist
  Future<void> deleteSetlist(String id) async {
    try {
      // Check if user is authenticated
      if (!await isAuthenticated()) {
        throw Exception('Authentication required. Please log in.');
      }

      final response = await _apiService.delete('/setlists/$id');

      if (response.statusCode != 200 && response.statusCode != 204) {
        throw Exception('Failed to delete setlist');
      }
    } catch (e) {
      debugPrint('Error deleting setlist: $e');
      if (e is DioException && e.response?.statusCode == 401) {
        throw Exception('Authentication required. Please log in.');
      }
      throw Exception('Failed to delete setlist: $e');
    }
  }

  // Add a song to a setlist
  Future<void> addSongToSetlist(String setlistId, String songId) async {
    try {
      // Check if user is authenticated
      if (!await isAuthenticated()) {
        throw Exception('Authentication required. Please log in.');
      }

      final response = await _apiService.post('/setlists/$setlistId/songs', data: {
        'songId': songId,
      });

      if (response.statusCode != 200 && response.statusCode != 201) {
        throw Exception('Failed to add song to setlist');
      }
    } catch (e) {
      debugPrint('Error adding song to setlist: $e');
      if (e is DioException && e.response?.statusCode == 401) {
        throw Exception('Authentication required. Please log in.');
      }
      throw Exception('Failed to add song to setlist: $e');
    }
  }

  // Add multiple songs to a setlist in a single API call
  Future<void> addMultipleSongsToSetlist(String setlistId, List<String> songIds) async {
    try {
      // Check if user is authenticated
      if (!await isAuthenticated()) {
        throw Exception('Authentication required. Please log in.');
      }

      if (songIds.isEmpty) {
        throw Exception('No songs provided to add to setlist');
      }

      debugPrint('Adding ${songIds.length} songs to setlist $setlistId');

      // Add timeout to prevent hanging
      final response = await _apiService.post('/setlists/$setlistId/songs/bulk', data: {
        'songIds': songIds,
      }).timeout(
        const Duration(seconds: 30), // 30 second timeout
        onTimeout: () {
          throw Exception('Request timed out. Please try again.');
        },
      );

      if (response.statusCode != 200 && response.statusCode != 201) {
        throw Exception('Failed to add songs to setlist');
      }

      debugPrint('Successfully added ${songIds.length} songs to setlist');
    } catch (e) {
      debugPrint('Error adding multiple songs to setlist: $e');
      if (e is DioException && e.response?.statusCode == 401) {
        throw Exception('Authentication required. Please log in.');
      }
      throw Exception('Failed to add songs to setlist: $e');
    }
  }

  // Remove a song from a setlist
  Future<void> removeSongFromSetlist(String setlistId, String songId) async {
    try {
      // Check if user is authenticated
      if (!await isAuthenticated()) {
        throw Exception('Authentication required. Please log in.');
      }

      final response = await _apiService.delete('/setlists/$setlistId/songs/$songId');

      if (response.statusCode != 200 && response.statusCode != 204) {
        throw Exception('Failed to remove song from setlist');
      }
    } catch (e) {
      debugPrint('Error removing song from setlist: $e');
      if (e is DioException && e.response?.statusCode == 401) {
        throw Exception('Authentication required. Please log in.');
      }
      throw Exception('Failed to remove song from setlist: $e');
    }
  }

  // Check if a song is in a setlist
  Future<bool> isSongInSetlist(String setlistId, String songId) async {
    try {
      // Check if user is authenticated
      if (!await isAuthenticated()) {
        throw Exception('Authentication required. Please log in.');
      }

      // Get the setlist with songs
      final setlist = await getSetlist(setlistId);

      // Check if the song is in the setlist
      if (setlist.songs == null || setlist.songs!.isEmpty) {
        return false;
      }

      // Check each song in the setlist
      for (final song in setlist.songs!) {
        if (song is Map<String, dynamic> && song['id'] == songId) {
          return true;
        }
      }

      return false;
    } catch (e) {
      debugPrint('Error checking if song is in setlist: $e');
      return false;
    }
  }
}
