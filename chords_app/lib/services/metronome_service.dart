import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

class MetronomeService extends ChangeNotifier {
  Timer? _timer;
  int _bpm = 120;
  int _currentBeat = 1;
  int _beatsPerMeasure = 4;
  bool _isRunning = false;
  bool _isMuted = false;
  final int _countInBeats = 4;
  bool _isCountingIn = false;
  int _countInRemaining = 0;

  // Advanced features
  MetronomeSound _sound = MetronomeSound.click;
  bool _accentFirstBeat = true;
  double _volume = 0.8;
  BeatPattern? _currentPattern;

  // Callbacks
  Function(int beat, bool isAccented)? onBeat;
  Function()? onCountInComplete;
  Function(int remaining)? onCountInTick;

  // Getters
  int get bpm => _bpm;
  int get currentBeat => _currentBeat;
  int get beatsPerMeasure => _beatsPerMeasure;
  bool get isRunning => _isRunning;
  bool get isMuted => _isMuted;
  bool get isCountingIn => _isCountingIn;
  int get countInRemaining => _countInRemaining;
  MetronomeSound get sound => _sound;
  bool get accentFirstBeat => _accentFirstBeat;
  double get volume => _volume;

  // Setters
  set bpm(int value) {
    if (value >= 40 && value <= 300) {
      _bpm = value;
      if (_isRunning) {
        _restart();
      }
      notifyListeners();
    }
  }

  set beatsPerMeasure(int value) {
    if (value >= 2 && value <= 12) {
      _beatsPerMeasure = value;
      _currentBeat = 1;
      notifyListeners();
    }
  }

  set sound(MetronomeSound value) {
    _sound = value;
    notifyListeners();
  }

  set accentFirstBeat(bool value) {
    _accentFirstBeat = value;
    notifyListeners();
  }

  set volume(double value) {
    if (value >= 0.0 && value <= 1.0) {
      _volume = value;
      notifyListeners();
    }
  }

  // Core metronome functions
  void start({bool withCountIn = true}) {
    if (_isRunning) return;

    if (withCountIn && _countInBeats > 0) {
      _startCountIn();
    } else {
      _startMetronome();
    }
  }

  void stop() {
    _timer?.cancel();
    _timer = null;
    _isRunning = false;
    _isCountingIn = false;
    _currentBeat = 1;
    _countInRemaining = 0;
    notifyListeners();
  }

  void pause() {
    _timer?.cancel();
    _timer = null;
    _isRunning = false;
    _isCountingIn = false;
    notifyListeners();
  }

  void resume() {
    if (!_isRunning) {
      _startMetronome();
    }
  }

  void toggleMute() {
    _isMuted = !_isMuted;
    notifyListeners();
  }

  void tapTempo() {
    // Implement tap tempo functionality
    _tapTempoTimes.add(DateTime.now());

    // Keep only last 8 taps
    if (_tapTempoTimes.length > 8) {
      _tapTempoTimes.removeAt(0);
    }

    if (_tapTempoTimes.length >= 2) {
      _calculateTapTempo();
    }
  }

  // Advanced tempo functions
  void increaseTempo({int amount = 5}) {
    bpm = (_bpm + amount).clamp(40, 300);
  }

  void decreaseTempo({int amount = 5}) {
    bpm = (_bpm - amount).clamp(40, 300);
  }

  void setTempoPercentage(double percentage) {
    // For practice mode - set tempo as percentage of original
    int originalTempo = _originalTempo ?? 120;
    bpm = (originalTempo * percentage).round().clamp(40, 300);
  }

  // Pattern-based metronome
  void setCustomPattern(BeatPattern pattern) {
    _currentPattern = pattern;
    _beatsPerMeasure = pattern.beats.length;
    notifyListeners();
  }

  void clearCustomPattern() {
    _currentPattern = null;
    notifyListeners();
  }

  // Private methods
  void _startCountIn() {
    _isCountingIn = true;
    _countInRemaining = _countInBeats;
    _currentBeat = 1;

    _timer = Timer.periodic(_getBeatDuration(), (timer) {
      if (_countInRemaining > 0) {
        _playCountInSound();
        onCountInTick?.call(_countInRemaining);
        _countInRemaining--;
        notifyListeners();
      } else {
        _isCountingIn = false;
        onCountInComplete?.call();
        _startMetronome();
      }
    });
  }

  void _startMetronome() {
    _isRunning = true;
    _currentBeat = 1;

    _timer = Timer.periodic(_getBeatDuration(), (timer) {
      _tick();
    });

    notifyListeners();
  }

  void _restart() {
    if (_isRunning) {
      _timer?.cancel();
      _startMetronome();
    }
  }

  void _tick() {
    bool isAccented = _shouldAccentBeat(_currentBeat);

    if (!_isMuted) {
      _playBeatSound(isAccented);
    }

    onBeat?.call(_currentBeat, isAccented);

    _currentBeat++;
    if (_currentBeat > _beatsPerMeasure) {
      _currentBeat = 1;
    }

    notifyListeners();
  }

  bool _shouldAccentBeat(int beat) {
    if (_currentPattern != null) {
      int index = (beat - 1) % _currentPattern!.beats.length;
      return _currentPattern!.beats[index].isAccented;
    }

    return _accentFirstBeat && beat == 1;
  }

  Duration _getBeatDuration() {
    return Duration(milliseconds: (60000 / _bpm).round());
  }

  void _playBeatSound(bool isAccented) {
    if (_isMuted) return;

    // Play different sounds based on accent and sound type
    switch (_sound) {
      case MetronomeSound.click:
        _playClickSound(isAccented);
        break;
      case MetronomeSound.beep:
        _playBeepSound(isAccented);
        break;
      case MetronomeSound.tick:
        _playTickSound(isAccented);
        break;
      case MetronomeSound.woodblock:
        _playWoodblockSound(isAccented);
        break;
    }
  }

  void _playCountInSound() {
    if (_isMuted) return;
    // Play a distinct count-in sound
    HapticFeedback.lightImpact();
  }

  void _playClickSound(bool isAccented) {
    // Implement actual sound playing
    // For now, use haptic feedback
    if (isAccented) {
      HapticFeedback.mediumImpact();
    } else {
      HapticFeedback.lightImpact();
    }
  }

  void _playBeepSound(bool isAccented) {
    // Different beep sounds
    HapticFeedback.lightImpact();
  }

  void _playTickSound(bool isAccented) {
    // Tick sound implementation
    HapticFeedback.lightImpact();
  }

  void _playWoodblockSound(bool isAccented) {
    // Woodblock sound implementation
    HapticFeedback.lightImpact();
  }

  // Tap tempo implementation
  final List<DateTime> _tapTempoTimes = [];
  int? _originalTempo;

  void _calculateTapTempo() {
    if (_tapTempoTimes.length < 2) return;

    List<int> intervals = [];
    for (int i = 1; i < _tapTempoTimes.length; i++) {
      int interval = _tapTempoTimes[i].difference(_tapTempoTimes[i - 1]).inMilliseconds;
      intervals.add(interval);
    }

    // Calculate average interval
    double avgInterval = intervals.reduce((a, b) => a + b) / intervals.length;

    // Convert to BPM
    int newBpm = (60000 / avgInterval).round().clamp(40, 300);

    bpm = newBpm;
  }

  void setOriginalTempo(int tempo) {
    _originalTempo = tempo;
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }
}

enum MetronomeSound {
  click,
  beep,
  tick,
  woodblock,
}

class BeatPattern {
  final String name;
  final List<Beat> beats;
  final String description;

  BeatPattern({
    required this.name,
    required this.beats,
    required this.description,
  });

  static List<BeatPattern> getCommonPatterns() {
    return [
      BeatPattern(
        name: "4/4 Standard",
        beats: [
          Beat(isAccented: true),
          Beat(isAccented: false),
          Beat(isAccented: false),
          Beat(isAccented: false),
        ],
        description: "Standard 4/4 time with accent on beat 1",
      ),
      BeatPattern(
        name: "3/4 Waltz",
        beats: [
          Beat(isAccented: true),
          Beat(isAccented: false),
          Beat(isAccented: false),
        ],
        description: "Waltz time with accent on beat 1",
      ),
      BeatPattern(
        name: "6/8 Compound",
        beats: [
          Beat(isAccented: true),
          Beat(isAccented: false),
          Beat(isAccented: false),
          Beat(isAccented: true),
          Beat(isAccented: false),
          Beat(isAccented: false),
        ],
        description: "6/8 time with accents on beats 1 and 4",
      ),
    ];
  }
}

class Beat {
  final bool isAccented;
  final double volume;
  final MetronomeSound? customSound;

  Beat({
    required this.isAccented,
    this.volume = 1.0,
    this.customSound,
  });
}
