name: chords_app
description: "Stuthi - Christian Song Chords & Lyrics App"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.7.2

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  shared_preferences: ^2.2.2

  # Firebase packages
  firebase_core: ^3.13.0
  firebase_auth: ^5.5.3
  firebase_messaging: ^15.2.5
  google_sign_in: ^6.3.0
  sign_in_with_apple: ^7.0.1
  # Removed flutter_facebook_auth due to compatibility issues
  # We'll implement Facebook login using a web view approach instead

  # Splash screen - REMOVED (using custom Flutter splash screen instead)
  # flutter_native_splash: ^2.3.10

  # State management

  # HTTP and API handling
  http: ^1.3.0
  dio: ^5.4.1
  crypto: ^3.0.3

  # State management
  provider: ^6.1.2

  # Local storage
  flutter_secure_storage: ^8.1.0

  # UI utilities
  flutter_svg: ^2.0.9
  cached_network_image: ^3.3.0
  intl: ^0.19.0
  timeago: ^3.6.1
  flutter_guitar_chord: ^0.0.3
  guitar_chord_library: ^0.0.2
  google_fonts: ^6.1.0
  url_launcher: ^6.3.1
  flutter_cache_manager: ^3.4.1
  share_plus: ^7.2.2
  qr_flutter: ^4.1.0
  # Removed flutter_local_notifications due to compatibility issues
  # Using custom SnackBar implementation instead of fluttertoast

  # Audio processing for guitar tuner
  permission_handler: ^11.3.0
  syncfusion_flutter_gauges: ^24.2.6
  youtube_player_iframe: ^5.2.1
  webview_flutter: ^4.7.0
  # Keeping the old package for reference
  # youtube_player_flutter: ^9.1.1

  # PDF generation and printing
  pdf: ^3.10.7
  printing: ^5.12.0
  path_provider: ^2.1.2

  # Practice Mode dependencies
  audioplayers: ^6.4.0
  wakelock_plus: ^1.2.8

  # Connectivity and offline support
  connectivity_plus: ^6.0.5

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  mockito: ^5.4.4
  build_runner: ^2.4.8
  flutter_launcher_icons: ^0.13.1

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# Flutter Launcher Icons configuration
flutter_launcher_icons:
  android: true
  ios: true
  image_path: "assets/images/appicon.png"
  min_sdk_android: 21
  # Android adaptive icon configuration
  adaptive_icon_background: "#121212"
  adaptive_icon_foreground: "assets/images/appicon.png"
  # Remove alpha channel for iOS App Store compliance
  remove_alpha_ios: true
  # Ensure proper scaling and monochrome support
  adaptive_icon_monochrome: "assets/images/appicon.png"
  # Platform-specific settings
  platforms:
    android:
      adaptive_icon_background: "#121212"
      adaptive_icon_foreground: "assets/images/appicon.png"
    ios:
      generate: true
      image_path: "assets/images/appicon.png"

# Flutter Native Splash configuration - DISABLED
# Commented out to use only custom Flutter splash screen
# flutter_native_splash:
#   color: "#121212"
#   image: assets/images/stuthi logo purple.png
#   android: true
#   ios: true
#   web: false
#   fullscreen: true

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/audio/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
